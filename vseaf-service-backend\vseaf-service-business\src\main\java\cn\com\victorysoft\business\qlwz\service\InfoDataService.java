package cn.com.victorysoft.business.qlwz.service;

import cn.com.victorysoft.business.qlwz.bean.InfoDataBean;
import cn.com.victorysoft.vseaf.core.mybatis.paging.bean.DataPaging;
import cn.com.victorysoft.vseaf.core.mybatis.paging.bean.PageRequest;
import cn.com.victorysoft.vseaf.core.repository.MybatisRepository;
import cn.com.victorysoft.vseaf.core.web.http.JsonMessage;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
@Component
public class InfoDataService {
   @Resource
   MybatisRepository mybatisRepository;

   private final String namespace = "cn.com.victorysoft.business.qlwz.dao.InfoDataMapper.";
    private final String namespace1 = "cn.com.victorysoft.business.qlwz.dao.InfoDataMonitorMapper.";

    /**
     * 查询信息门户
     */
    public JsonMessage queryInfoDataList(Map params) {
        int pageIndex = (Integer) params.get("pageIndex");
        int pageSize = (Integer) params.get("pageSize");
        PageRequest pageRequest =new PageRequest<>(pageIndex,pageSize,params);
        DataPaging dataPaging = mybatisRepository.selectPaging(namespace+"queryInfoDataList", pageRequest);
        return new JsonMessage().success(dataPaging);
    }
    /**
     * 保存信息门户
     */
    @Transactional
    public JsonMessage saveInfoData(InfoDataBean infoDataBean){
        Map result=new HashMap();
        try{
            mybatisRepository.update(namespace+"saveInfoData", infoDataBean);
            if(infoDataBean.getGlyth()==1){//管理员退回门户信息至草稿
                mybatisRepository.delete(namespace1+"deleteInfoDataMonitor", infoDataBean.getInfoId());
            }
            result.put("status","success");
            result.put("message","保存成功");
        }catch (Exception e){
            result.put("status","fail");
            result.put("message","保存失败");
        }

        return new JsonMessage().success(result);
    }
    /**
     * 删除信息门户
     */
    public JsonMessage deleteInfoData(String infoId){
        Map resultMap = new HashMap();
        try {
            mybatisRepository.delete(namespace + "deleteInfoData", infoId);
            resultMap.put("status","success");
            resultMap.put("message","删除成功");
        }catch (Exception e){
            resultMap.put("status","fail");
            resultMap.put("message","删除失败");
        }
        return new JsonMessage().success(resultMap);
    }
    /**
     * 根据主键infoId查询信息门户
     */
    public JsonMessage queryInfoDataById(InfoDataBean infoDataBean){
        infoDataBean = mybatisRepository.selectById(namespace+"queryInfoDataById", infoDataBean.getInfoId());
        return new JsonMessage().success(infoDataBean);
    }
    /**
     * 查询纪发课堂的前三条最新数据
     */
    public JsonMessage queryInfoData(Map params){
        int pageIndex=(Integer) params.get("pageIndex");
        int pageSize=(Integer) params.get("pageSize");
        PageRequest pageRequest=new PageRequest(pageIndex,pageSize);
        DataPaging dataPaging=mybatisRepository.selectPaging(namespace+"queryInfoData",pageRequest);
        return new JsonMessage().success(dataPaging);
    }
    /**
     * 查询纪发课堂提交的所有列表
     */
    public JsonMessage queryInfoList(Map params){
        int pageIndex=(Integer) params.get("pageIndex");
        int pageSize=(Integer) params.get("pageSize");
        PageRequest pageRequest=new PageRequest(pageIndex,pageSize,params);
        DataPaging dataPaging=mybatisRepository.selectPaging(namespace+"queryInfoList",pageRequest);
        return new JsonMessage().success(dataPaging);
    }
    /**
     * 通过上传文件的业务id查询出类型是视频类型的信息
     */
    public JsonMessage queryVideo(InfoDataBean infoDataBean){
        infoDataBean=mybatisRepository.selectOne(namespace+"queryVideo",infoDataBean);
        return new JsonMessage().success(infoDataBean);
    }

    public Map<String, Object> queryInfoWebList(Map<String, Object> param) {
        Map<String, Object> resultMap = new HashMap<>();
        Integer pageNum = param.get("page") == null ? 1 : Integer.parseInt(param.get("page").toString());
        Integer pageSize = param.get("size") == null ? 10 : Integer.parseInt(param.get("size").toString());
        PageRequest pageRequest = new PageRequest(pageNum, pageSize, param);
        DataPaging<Map> dataPaging = mybatisRepository.selectPaging(namespace + "queryInfoWebList", pageRequest);
        resultMap.put("rows",dataPaging.getRows());
        resultMap.put("total",dataPaging.getTotal());
        return resultMap;
    }

    public Map<String, Object> queryInfoWebOne(Map<String, Object> param) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String,Object> map =mybatisRepository.selectOne(namespace+"queryInfoWebOne",param);
        resultMap.put("form",map);
        return resultMap;
    }
}
