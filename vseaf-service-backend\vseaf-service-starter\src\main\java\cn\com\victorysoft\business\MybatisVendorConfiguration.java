package cn.com.victorysoft.business;

import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.mapping.VendorDatabaseIdProvider;
import org.mybatis.spring.boot.autoconfigure.ConfigurationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.Properties;

/**
 * 工具类
 * <AUTHOR> @date
 * @return
 */
@Configuration
public class MybatisVendorConfiguration {

    private final static Logger log = LoggerFactory.getLogger(MybatisVendorConfiguration.class);
    /**
     * 方法
     * <AUTHOR> @date
     * @return
     */
    @Bean
    ConfigurationCustomizer vendorConfigurationCustomizer(DataSource dataSource) {
        return (configuration) -> {
            try {
                configuration.setDatabaseId(vendorDatabaseIdProvider().getDatabaseId(dataSource));
            } catch (SQLException e) {
                log.error(e.getMessage(), e);
            }
        };
    }
    /**
     * 方法
     * <AUTHOR> @date
     * @return
     */
    DatabaseIdProvider vendorDatabaseIdProvider() {
        DatabaseIdProvider databaseIdProvider = new VendorDatabaseIdProvider();
        Properties properties = new Properties();
        properties.setProperty("Oracle", "oracle");
        properties.setProperty("MySQL", "mysql");
        properties.setProperty("PostgreSQL", "postgresql");
        properties.setProperty("Microsoft SQL Server", "sqlserver");
        databaseIdProvider.setProperties(properties);
        return databaseIdProvider;
    }
}
