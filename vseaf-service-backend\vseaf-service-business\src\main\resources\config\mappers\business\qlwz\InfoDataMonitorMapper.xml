<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.victorysoft.business.qlwz.dao.InfoDataMonitorMapper">
    <!--    保存审核信息-->
    <insert id="saveInfoDataMonitor" parameterType="cn.com.victorysoft.business.qlwz.bean.InfoDataMonitorBean">
        INSERT INTO INFO_DATA_MONITOR (
        MONITOEID, INFOID, USERID, USERNAME, ORGID, ORGNAME, STATUS, OPONION, TASKTIME
        )
        VALUES (
        <if test="monitoeId != null and monitoeId != ''">#{monitoeId},</if>
        <if test="infoId != null and infoId != ''">#{infoId},</if>
        <if test="userId != null and userId != ''">#{userId},</if>
        <if test="userName != null and userName != ''">#{userName},</if>
        <if test="orgId != null and orgId != ''">#{orgId},</if>
        <if test="orgName != null and orgName != ''">#{orgName},</if>
        <if test="status != null and status != ''">#{status},</if>
        <if test="oponion != null and oponion != ''">#{oponion,jdbcType=VARCHAR},</if>
        SYSDATE
        )
    </insert>
<!--    根据infoId删除审核流程信息-->
    <delete id="deleteInfoDataMonitor" parameterType="java.lang.String">
        DELETE FROM INFO_DATA_MONITOR WHERE INFOID = #{infoId}
    </delete>

</mapper>