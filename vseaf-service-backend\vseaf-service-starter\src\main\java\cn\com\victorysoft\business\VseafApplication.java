package cn.com.victorysoft.business;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;


/**
 * 工具类
 * <AUTHOR> @date
 * @return
 */
@SpringBootApplication
@ComponentScan({"cn.com.victorysoft.authm", "cn.com.victorysoft.business"})
@MapperScan("cn.com.victorysoft.business.**.dao")
@ServletComponentScan(basePackages = "cn.com.victorysoft.business.filter")
public class VseafApplication {

    public static void main(String[] args) {
        SpringApplication.run(VseafApplication.class, args);
    }

}
