package cn.com.victorysoft.business.common.controller;

import cn.com.victorysoft.business.common.bean.*;
import cn.com.victorysoft.business.common.service.DmbService;
import cn.com.victorysoft.vseaf.core.web.http.JsonMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


/**
 * 代码 controller
 * @ClassName: DmbController
 * @DATE: 2023/06/09 09:45
 * <AUTHOR>
 */
@RestController
@Api(tags = "dmController")
@RequestMapping("/dmController")
public class DmbController {

    @Autowired
    private DmbService dmbService;

    /**
     * 查询代码信息
     * @return JsonMessage
     * <AUTHOR>
     * @Date 2021/10/09 09:45
     */
    @PostMapping("/queryDmList")
    @ApiOperation(value = "queryDmList", notes = "查询代码信息")
    public JsonMessage queryDmList(@RequestBody DmbBean bean) {
        return dmbService.queryDmList(bean);
    }


    /**
     * 查询组织机构
     * @return JsonMessage
     * <AUTHOR>
     * @Date 2021/10/09 09:45
     */
    @PostMapping("/queryOrgList")
    @ApiOperation(value = "queryOrgList", notes = "查询组织机构")
    public JsonMessage queryOrgList(@RequestBody DepartmentBean bean) {
        return dmbService.queryOrgList(bean,"no");
    }

    /**
     * 查询组织机构树
     * @return JsonMessage
     * <AUTHOR>
     * @Date 2021/10/09 09:45
     */
    @PostMapping("/queryOrgTreeList")
    @ApiOperation(value = "queryOrgTreeList", notes = "查询组织机构")
    public JsonMessage queryOrgTreeList(@RequestBody DepartmentBean bean) {
        return dmbService.queryOrgList(bean,"yes");
    }


    /**
     * 修改用户密码
     * @return JsonMessage
     * <AUTHOR>
     * @Date 2021/11/25
     */
    @PostMapping("/updateEntUserById")
    @ApiOperation(value = "updateUserById", notes = "查询组织机构是否为管理部门")
    public JsonMessage updateUserById(@RequestBody EntUser entUser) {
        return dmbService.updateUserById(entUser);
    }


}
