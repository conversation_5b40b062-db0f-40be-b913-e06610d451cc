package cn.com.victorysoft.business.qlwz.service;

import cn.com.victorysoft.business.qlwz.bean.InfoDataBean;
import cn.com.victorysoft.business.qlwz.bean.InfoDataMonitorBean;
import cn.com.victorysoft.vseaf.core.mybatis.paging.bean.DataPaging;
import cn.com.victorysoft.vseaf.core.mybatis.paging.bean.PageRequest;
import cn.com.victorysoft.vseaf.core.repository.MybatisRepository;
import cn.com.victorysoft.vseaf.core.web.http.JsonMessage;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
@Component
public class InfoDataMonitorService {
   @Resource
   MybatisRepository mybatisRepository;

   private final String namespace = "cn.com.victorysoft.business.qlwz.dao.InfoDataMonitorMapper.";
    private final String namespace1 = "cn.com.victorysoft.business.qlwz.dao.InfoDataMapper.";

    /**
     * 保存信息门户
     */
    @Transactional
    public JsonMessage saveInfoDataMonitor(InfoDataBean infoDataBean){
        Map resultMap = new HashMap();
        try {
            //先保存信息门户
            mybatisRepository.update(namespace1 + "saveInfoData", infoDataBean);
            //保存审核信息门户
            mybatisRepository.insert(namespace + "saveInfoDataMonitor", infoDataBean);
            resultMap.put("status","success");
            resultMap.put("message","保存成功");
        }catch (Exception e){
            resultMap.put("status","fail");
            resultMap.put("message","保存失败");
        }

        return new JsonMessage().success(resultMap);
    }


}
