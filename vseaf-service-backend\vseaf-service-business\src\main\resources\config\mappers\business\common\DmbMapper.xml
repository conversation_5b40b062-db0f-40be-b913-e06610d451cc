<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.victorysoft.business.common.dao.DmbMapper">


    <!-- 查询代码列表 -->
    <select id="queryDmList" resultType="cn.com.victorysoft.business.common.bean.DmbBean"
            parameterType="cn.com.victorysoft.business.common.bean.DmbBean" databaseId="mysql">
        SELECT LB.LBID,LB.LBMC,LBMX.CODE,LBMX.MZMC,LBMX.PID,LBMX.PXH,LBMX.BH
        FROM BASE_DMLB LB
        INNER JOIN BASE_DMLBMX LBMX
        ON LB.LBID = LBMX.LBID
        WHERE LB.YXBZ = '1'
        AND LBMX.YXBZ = '1'
        AND SSXT LIKE '%SJ%'
        <if test="lbid != null and lbid != ''">
            AND LB.LBID = #{lbid}
        </if>
        <if test="pid != null and pid != ''">
            AND LBMX.PID = #{pid}
        </if>
        <if test="pids != null and pids != ''">
            AND LBMX.PID IN
          <foreach collection="pids" item="item" separator="," open="(" close=")">
               #{item}
          </foreach>
        </if>
        ORDER BY LBMX.PXH
    </select>

    <!--查询组织机构-->
    <select id="queryOrgList"
            parameterType="cn.com.victorysoft.business.common.bean.DepartmentBean"
            resultType="cn.com.victorysoft.business.common.bean.DepartmentBean"
            databaseId="mysql">
        SELECT ORGNA_ID,
        ORGNA_NAME,
        PORGNA_ID,
        ORGNA_CODE, ORGNA_FLOOR_NUM
        FROM ENT_ORGANIZATION O
        WHERE ENABLED = '1'
        <if test="orgnaName != null and orgnaName != ''">
            AND ORGNA_NAME like concat('%', #{orgnaName}, '%')
        </if>
        <if test="orgnaId != null and orgnaId != ''">
            AND ORGNA_ID = #{orgnaId}
        </if>
        <if test="porgnaId != null and porgnaId != ''">
            AND PORGNA_ID = #{porgnaId}
        </if>
        <if test="orgnaFloorCode != null and orgnaFloorCode != ''">
            AND ORGNA_FLOOR_NUM &lt;= #{orgnaFloorCode}
        </if>
        <if test="orgnaType != null and orgnaType != ''">
            AND ORGNA_TYPE = #{orgnaType}
        </if>
        ORDER BY ORGNA_ORDER
    </select>

    <select id="queryOrgListWithFloor" parameterType="cn.com.victorysoft.business.common.bean.DepartmentBean"
            resultType="cn.com.victorysoft.business.common.bean.DepartmentBean" databaseId="mysql">
        SELECT ORGNA_ID, ORGNA_NAME, PORGNA_ID, ORGNA_CODE, '2' AS ORGNA_FLOOR_NUM
        FROM ENT_ORGANIZATION O
        WHERE ENABLED = '1'
        <if test="orgnaName != null and orgnaName != ''">
            AND ORGNA_NAME like concat('%', #{orgnaName}, '%')
        </if>
        <if test="orgnaId != null and orgnaId != ''">
            AND ORGNA_ID = #{orgnaId}
        </if>
        <if test="porgnaId != null and porgnaId != ''">
            AND PORGNA_ID = #{porgnaId}
        </if>
        <if test="orgnaFloorCode != null and orgnaFloorCode != ''">
            AND ORGNA_FLOOR_NUM &lt;= #{orgnaFloorCode}
        </if>
        <if test="orgnaType != null and orgnaType != ''">
            AND ORGNA_TYPE = #{orgnaType}
        </if>
        ORDER BY ORGNA_ORDER
    </select>

    <!-- 查询组织机构 带有子节点数量 -->
    <select id="queryOrgListWithChild" parameterType="cn.com.victorysoft.business.common.bean.DepartmentBean"
            resultType="cn.com.victorysoft.business.common.bean.DepartmentBean" databaseId="mysql">
        SELECT O.ORGNA_ID, O.ORGNA_NAME, O.PORGNA_ID, O.ORGNA_CODE, O.ORGNA_FLOOR_NUM, COUNT(O2.ORGNA_ID) leafTotal
        FROM ENT_ORGANIZATION O
            LEFT JOIN ENT_ORGANIZATION O2 ON O2.PORGNA_ID = O.ORGNA_ID AND O2.ENABLED = '1'
        WHERE O.ENABLED = '1'
        <if test="orgnaName != null and orgnaName != ''">
            AND O.ORGNA_NAME like concat('%', #{orgnaName}, '%')
        </if>
        <if test="orgnaId != null and orgnaId != ''">
            AND O.ORGNA_ID = #{orgnaId}
        </if>
        <if test="porgnaId != null and porgnaId != ''">
            AND O.PORGNA_ID = #{porgnaId}
        </if>
        <if test="orgnaFloorCode != null and orgnaFloorCode != ''">
            AND O.ORGNA_FLOOR_NUM &lt;= #{orgnaFloorCode}
        </if>
        GROUP BY O.ORGNA_ID,O.ORGNA_NAME,O.PORGNA_ID,O.ORGNA_CODE,O.ORGNA_FLOOR_NUM
    </select>

    <!-- 查询组织机构是否为管理部门 -->
    <select id="queryOrgGlbm"
            parameterType="cn.com.victorysoft.business.common.bean.DepartmentBean"
            resultType="java.lang.String">
        SELECT GLBM
        FROM ENT_ORGANIZATION
        WHERE ORGNA_ID = #{orgnaId}
    </select>


    <update id="updateEntUserPassword" parameterType="cn.com.victorysoft.business.common.bean.EntUser">
        update ENT_USER
        set user_password = #{userPassword}
        WHERE user_id = #{userId}
    </update>


</mapper>
