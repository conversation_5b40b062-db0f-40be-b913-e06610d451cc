<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.victorysoft.business.common.dao.AccesslogMapper">

    <!--  存储日志信息  -->
    <insert id="insertLog" parameterType="java.util.Map">
        INSERT INTO SYS_LOG
            (ID, AREA_ID, TENANT_ID, LOG_CONTENT, METHOD, PARAMES, DEVICE_IP, LOG_TYPE, OPRATE_TYPE, OPRATE_USER, OPRATE_TIME, OPRATE_RESULT)
        VALUES
            ( REPLACE(UUID(), '-', '') , #{AREAID} , #{TENANTID} , #{LOGCONTENT} , #{METHOD} , #{PARAMES} , #{DEVICEIP} , #{LOGTYPE} , #{OPRATETYPE} , #{OPRATEUSER} , now() , #{OPRATERESULT})
    </insert>

</mapper>
