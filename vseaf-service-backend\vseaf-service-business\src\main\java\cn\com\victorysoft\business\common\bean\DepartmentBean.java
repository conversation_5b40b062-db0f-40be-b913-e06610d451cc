package cn.com.victorysoft.business.common.bean;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * 组织机构
 */
@ApiModel(value = "组织机构", description = "组织机构")
@Data
public class DepartmentBean {

    private String orgnaId;
    private String orgnaName;
    private String porgnaId;
    private String orgnaCode;
    private String orgnaType;
    private String orgnaLevel;
    private String orgnaFloorCode;
    private String orgnaFloorName;
    private List<DepartmentBean> children;

    private String orgnaFloorNum;

    // 二级单位ID
    private String orgnaTowId;
    // 二级单位名称
    private String orgnaTowName;
    // 子节点总数
    private String leafTotal;
    // 树
    private String treeFlag;

}
