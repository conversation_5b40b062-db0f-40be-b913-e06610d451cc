package cn.com.victorysoft.business.qlwz.controller;

import cn.com.victorysoft.business.common.util.VsUtil;
import cn.com.victorysoft.business.qlwz.bean.InfoDataBean;
import cn.com.victorysoft.business.qlwz.service.InfoDataService;
import cn.com.victorysoft.vseaf.core.web.http.JsonMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/qlwz")
@Api(tags = "qlwz")
public class InfoDataController {
    @Autowired
    InfoDataService infoDataService;

    /**
     * 加载信息数据
     * 2024/11/21
     */
    @PostMapping("/queryInfoDataList")
    @ApiOperation(value = "查询门户信息list", notes = "queryInfoDataList")
    public JsonMessage queryInfoDataList(@RequestBody Map params) {
        return infoDataService.queryInfoDataList(params);
    }
    /**
     * 保存信息数据
     * 2024/11/21
     */
    @PostMapping("/saveInfoData")
    @ApiOperation(value = "保存门户信息", notes = "saveInfoData")
    public JsonMessage saveInfoData(@RequestBody InfoDataBean infoDataBean) {
        return infoDataService.saveInfoData(infoDataBean);
    }
    /**
     * 删除门户信息
     * 2024/11/21
     */
     @GetMapping("/deleteInfoData")
     @ApiOperation(value = "删除门户信息", notes = "deleteInfoData")
     public JsonMessage deleteInfoData(String infoId) {
         return infoDataService.deleteInfoData(infoId);
     }
    /**
     * 根据主键infoId查询门户信息
     * 2024/11/21
     */
    @PostMapping("/queryInfoDataById")
    @ApiOperation(value = "根据主键infoId查询门户信息", notes = "queryInfoDataById")
    public JsonMessage queryInfoDataById(@RequestBody InfoDataBean infoDataBean) {
        return infoDataService.queryInfoDataById(infoDataBean);
    }
    /**
     * 查询纪发课堂的前三条最新数据
     */
    @PostMapping("/queryInfoData")
    @ApiOperation(value = "查询纪发课堂审核意见列表", notes = "查询纪发课堂审核意见列表")
    public JsonMessage queryInfoData(@RequestBody Map params){
        return infoDataService.queryInfoData(params);
    }
    /**
     * 查询纪发课堂的所有视频
     */
    @PostMapping("/queryInfoList")
    @ApiOperation(value = "查询纪发课堂提交的所有视频列表", notes = "查询纪发课堂提交的所有视频列表")
    public JsonMessage queryInfoList(@RequestBody Map params){
        return infoDataService.queryInfoList(params);
    }
    /**
     * 通过上传文件的业务id查询出类型是视频类型的信息
     */
    @PostMapping("/queryVideo")
    @ApiOperation(value = "通过上传文件的业务id查询出类型是视频类型的信息", notes = "queryVideo")
    public JsonMessage queryVideo(@RequestBody InfoDataBean infoDataBean){
        return infoDataService.queryVideo(infoDataBean);
    }

    /**
     * @return
     * <AUTHOR>
     * @description 查询网站信息列表
     * @date 2024/3/5 11:00
     */
    @GetMapping("/queryInfoWebList")
    @ApiOperation(value = "queryInfoWebList ", notes = "查询网站信息列表")
    public JsonMessage queryInfoWebList(HttpServletRequest request) throws Exception {
        Map<String,Object> param = VsUtil.getMap(request);
        Map<String, Object> resultMap = new HashMap<>();;
        try {
            resultMap = infoDataService.queryInfoWebList(param);
            resultMap.put("status", "success");
            resultMap.put("msg", "成功");
        } catch (Exception e) {
            resultMap.put("status", "failed");
            resultMap.put("msg", e.getMessage());
        }
        return new JsonMessage().success(resultMap);
    }

    /**
     * @return
     * <AUTHOR>
     * @description 查询网站信息单条
     * @date 2024/3/5 11:00
     */
    @GetMapping("/queryInfoWebOne")
    @ApiOperation(value = "queryInfoWebOne ", notes = "查询网站信息单条")
    public JsonMessage queryInfoWebOne(HttpServletRequest request) throws Exception {
        Map<String,Object> param = VsUtil.getMap(request);
        Map<String, Object> resultMap = new HashMap<>();;
        try {
            resultMap = infoDataService.queryInfoWebOne(param);
            resultMap.put("status", "success");
            resultMap.put("msg", "成功");
        } catch (Exception e) {
            resultMap.put("status", "failed");
            resultMap.put("msg", e.getMessage());
        }
        return new JsonMessage().success(resultMap);
    }
}
