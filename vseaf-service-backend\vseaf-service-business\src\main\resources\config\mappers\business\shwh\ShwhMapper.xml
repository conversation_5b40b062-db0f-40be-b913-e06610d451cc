<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.victorysoft.business.shwh.dao.ShwhMapper">

    <!--  查询审核人信息列表  -->
    <select id="selectShwhList" parameterType="cn.com.victorysoft.business.shwh.bean.ShwhBean"
            resultType="cn.com.victorysoft.business.shwh.bean.ShwhBean">

        SELECT I.PZID,
               I.SHRID,
               I.SHRMC,
               I.SHDWID,
               I.SHDWMC,
               I.CJRID,
               I.CJRMC,
               TO_CHAR(I.CJSJ, 'YYYY-MM-DD HH24:MI:SS') CJSJ,
               T.USER_LOGINNAME SHRZH
        FROM INFO_SHRPZ I
        LEFT JOIN ENT_USER T ON I.SHRID = T.USER_ID
        <where>
            <if test="SHRMC != null and SHRMC != ''">
                AND I.SHRMC LIKE '%'||#{SHRMC}||'%'
            </if>
            <if test="SHRZH != null and SHRZH != ''">
                AND T.USER_LOGINNAME LIKE '%'||#{SHRZH}||'%'
            </if>
        </where>
        ORDER BY I.CJSJ DESC
    </select>

    <!-- 删除单个审核人信息 -->
    <delete id="deleteOneShwh" parameterType="java.lang.String">
        DELETE FROM INFO_SHRPZ WHERE PZID = #{PZID}
    </delete>

    <!-- 保存审核人信息 -->
    <insert id="insertShwhList" parameterType="cn.com.victorysoft.business.shwh.bean.ShwhBean">
        INSERT INTO INFO_SHRPZ (PZID, SHRID, SHRMC, SHDWID, SHDWMC, CJRID, CJRMC, CJSJ)
        VALUES (#{PZID}, #{SHRID}, #{SHRMC}, #{SHDWID}, #{SHDWMC}, #{CJRID}, #{CJRMC}, #{CJSJ})
    </insert>

    <!--  查询人员选择列表  -->
    <select id="selectUserSelectInfo" parameterType="cn.com.victorysoft.business.shwh.bean.ShwhBean"
            resultType="cn.com.victorysoft.business.shwh.bean.ShwhBean">
        SELECT T.USER_ID USERID,
               T.USER_NAME USERNAME,
               T.USER_LOGINNAME USERLOGINNAME,
               S.ORGNA_ID ORGNAID,
               S.ORGNA_NAME ORGNANAME
        FROM ENT_USER T
        JOIN ENT_ORGANIZATION S ON T.ORGNA_ID = S.ORGNA_ID
        WHERE T.ENABLED = '1'
        <if test="USERNAME != null and USERNAME != ''">
            AND T.USER_NAME LIKE '%'||#{USERNAME}||'%'
        </if>
        ORDER BY T.USER_NAME
    </select>

</mapper>
