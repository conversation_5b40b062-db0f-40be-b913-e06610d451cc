<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.com.victorysoft</groupId>
    <artifactId>vseaf-service-business</artifactId>
    <packaging>jar</packaging>

    <parent>
        <groupId>cn.com.victorysoft</groupId>
        <artifactId>vseaf-service-backend</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.4</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.15</version>
        </dependency>
        <dependency>
            <groupId>cn.com.victorysoft</groupId>
            <artifactId>vseaf-spring-boot</artifactId>
            <exclusions>
            	<exclusion>
            		<groupId>org.apache.poi</groupId>
            		<artifactId>poi-ooxml</artifactId>
            	</exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>cn.com.victorysoft</groupId>
            <artifactId>vseaf-spring-boot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>cn.com.victorysoft</groupId>
            <artifactId>vseaf-component-fileupload</artifactId>
            <version>4.4.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-mongodb</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
		 <groupId>com.jacob</groupId>
		 <artifactId>jacob</artifactId>
		 <version>1.18</version>
		</dependency>
        <dependency>
            <groupId>cn.com.victorysoft</groupId>
            <artifactId>vseaf4-util</artifactId>
            <version>4.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

        <dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.6</version>
		</dependency>

	    <dependency>
	      <groupId>org.javatuples</groupId>
	      <artifactId>javatuples</artifactId>
	      <version>1.2</version>
	    </dependency>
	    <dependency>
	      <groupId>org.apache.httpcomponents</groupId>
	      <artifactId>httpcore</artifactId>
	      <version>4.4.10</version>
	    </dependency>
	    <dependency>
	      <groupId>org.apache.httpcomponents</groupId>
	      <artifactId>httpclient</artifactId>
	      <version>4.5.6</version>
	    </dependency>
	    <dependency>
	      <groupId>org.apache.httpcomponents</groupId>
	      <artifactId>httpmime</artifactId>
	      <version>4.5.6</version>
	    </dependency>


        <dependency>
            <groupId>cn.com.victorysoft</groupId>
            <artifactId>vseaf-component-authm-client-core</artifactId>
            <version>1.1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>


        <!-- 以spring-boot-starter的方式引用 引入 magic-api -->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>spring-boot-starter-redis</groupId>
                    <artifactId>1.3.2.RELEASE</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.28</version>
        </dependency>

        <dependency>
            <groupId>org.ssssssss</groupId>
            <artifactId>magic-api-spring-boot-starter</artifactId>
            <version>2.0.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>

    </dependencies>
</project>
