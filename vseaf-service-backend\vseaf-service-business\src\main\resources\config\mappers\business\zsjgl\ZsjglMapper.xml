<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.victorysoft.business.zsjgl.dao.ZsjglMapper">

    <!-- 查询分页列表 -->
    <select id="selectPagingZsjList"
            parameterType="cn.com.victorysoft.business.zsjgl.bean.Dmlb"
            resultType="cn.com.victorysoft.business.zsjgl.bean.Dmlb">
        SELECT LBID, LBMC, PXH, SSXT, YXBZ FROM BASE_DMLB ORDER BY PXH ,YXBZ
	</select>

    <!-- 查询明细分页列表 -->
    <select id="queryZsjMxList" resultType="cn.com.victorysoft.business.zsjgl.bean.DmlbMx"
            parameterType="cn.com.victorysoft.business.zsjgl.bean.DmlbMx">
        SELECT DMMXID,CODE, MZMC, LBID, PID, PXH, YXBZ FROM BASE_DMLBMX WHERE LBID = #{lbid} ORDER BY PXH, YXBZ
    </select>


    <!-- 明细数据的删除 -->
    <delete id="delectData" parameterType="java.lang.String">
        DELETE FROM BASE_DMLBMX WHERE DMMXID = #{dmmxid}
    </delete>

    <!-- 主数据添加 -->
    <insert id="insertData" parameterType="cn.com.victorysoft.business.zsjgl.bean.Dmlb">
        INSERT INTO BASE_DMLB (LBID, LBMC, PXH, SSXT, YXBZ) VALUES (#{lbid}, #{lbmc}, #{pxh}, #{ssxt}, #{yxbz})
    </insert>

    <!-- 判断当前主数据是否存在 -->
    <select id="queryTjList" resultType="cn.com.victorysoft.business.zsjgl.bean.Dmlb">
        SELECT LBID, LBMC, PXH, YXBZ, SSXT FROM BASE_DMLB WHERE 1 = 1
        <if test="lbid != null and lbid != ''">
            AND LBID = #{lbid}
        </if>
    </select>

    <!-- 主数据明细添加 -->
    <insert id="insertMxData" parameterType="cn.com.victorysoft.business.zsjgl.bean.DmlbMx">
        INSERT INTO BASE_DMLBMX (DMMXID,CODE, MZMC, LBID, PXH, YXBZ) VALUES (#{dmmxid}, #{code}, #{mzmc}, #{lbid}, #{pxh}, #{yxbz})
    </insert>

    <!-- 判断当前主数据明细是否存在 -->
    <select id="queryZsjTjMxList" resultType="cn.com.victorysoft.business.zsjgl.bean.DmlbMx"
            parameterType="cn.com.victorysoft.business.zsjgl.bean.DmlbMx">
        SELECT CODE, MZMC, LBID, PID, PXH, YXBZ FROM BASE_DMLBMX WHERE LBID = #{lbid, jdbcType=VARCHAR}
        <if test="code != null and code != ''">
            AND CODE = #{code, jdbcType=VARCHAR}
        </if>
    </select>
    <!-- 主数据明细更新-->
    <update id="updateMx" parameterType="cn.com.victorysoft.business.zsjgl.bean.DmlbMx">
        UPDATE BASE_DMLBMX SET MZMC = #{mzmc}, PXH = #{pxh}, YXBZ = #{yxbz}  WHERE DMMXID = #{dmmxid}
    </update>
    <!-- 主数据更新-->
    <update id="updateZ" parameterType="cn.com.victorysoft.business.zsjgl.bean.Dmlb">
        UPDATE BASE_DMLB SET LBMC = #{lbmc}, PXH = #{pxh}, SSXT = #{ssxt}, YXBZ = #{yxbz} WHERE LBID = #{lbid}
    </update>

    <!--根据主数据id删除明细表-->
    <delete id="delectMx" parameterType="java.lang.String">
        DELETE FROM BASE_DMLBMX WHERE LBID = #{lbid}
    </delete>

    <!--根据主数据id删除主数据表-->
    <delete id="delectZ" parameterType="java.lang.String">
        DELETE FROM BASE_DMLB WHERE LBID = #{lbid}
    </delete>


</mapper>