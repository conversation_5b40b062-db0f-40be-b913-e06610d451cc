package cn.com.victorysoft.business.shwh.bean;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @package: cn.com.victorysoft.business.shwh.bean
 * @className: ShwhBean
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2025/7/22 16:34
 */
@Data
@ApiModel(value = "审核维护", description = "审核维护")
public class ShwhBean {

    // 主键ID
    @JsonProperty("PZID")
    private String PZID;

    // 审核人ID
    @JsonProperty("SHRID")
    private String SHRID;

    // 审核人名称
    @JsonProperty("SHRMC")
    private String SHRMC;

    // 审核人账号
    @JsonProperty("SHRZH")
    private String SHRZH;

    // 审核单位ID
    @JsonProperty("SHDWID")
    private String SHDWID;

    // 审核单位名称
    @JsonProperty("SHDWMC")
    private String SHDWMC;

    // 创建人ID
    @JsonProperty("CJRID")
    private String CJRID;

    // 创建人名称
    @JsonProperty("CJRMC")
    private String CJRMC;

    // 创建时间
    @JsonProperty("CJSJ")
    private String CJSJ;

    // 人员ID
    @JsonProperty("USERID")
    private String USERID;

    // 人员名称
    @JsonProperty("USERNAME")
    private String USERNAME;

    // 人员账号
    @JsonProperty("USERLOGINNAME")
    private String USERLOGINNAME;

    // 人员单位ID
    @JsonProperty("ORGNAID")
    private String ORGNAID;

    // 人员单位名称
    @JsonProperty("ORGNANAME")
    private String ORGNANAME;

    // 页数
    private String pageIndex;

    // 每页记录数
    private String pageSize;

}
