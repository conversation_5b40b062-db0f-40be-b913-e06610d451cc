package cn.com.victorysoft.business.common.util;

import cn.com.victorysoft.vseaf.core.repository.MybatisRepository;
import cn.com.victorysoft.vseaf.util.StringUtil;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;

/**
 * 工具类
 * <AUTHOR> @date
 * @return
 */
@RestController
@RequestMapping("/pdf")
public class PdfUtil {

    @Autowired
    MybatisRepository mybatisRepository;

    private final static Logger log = LoggerFactory.getLogger(PdfUtil.class);

    /**
     * 方法
     * <AUTHOR> @date
     * @return
     */
    @RequestMapping(value = "/showpdf")
    public void showpdf(HttpServletRequest request, HttpServletResponse response) {
        try {
            String fileId = StringUtil.null2blank(request.getParameter("fileId"));
            if ("fileId".equals(fileId)) {
                return;
            }
            String filePath = mybatisRepository
                    .selectById("cn.com.victorysoft.business.common.dao.DmbMapper.selectFilePath",
                            fileId);
            if ("".equals(StringUtil.null2blank(filePath))) {
                return;
            }
            File file = new File(filePath);
            FileInputStream fileInputStream = new FileInputStream(file);
            response.setHeader("Content-Type", "application/pdf");
            OutputStream outputStream = response.getOutputStream();
            IOUtils.write(IOUtils.toByteArray(fileInputStream), outputStream);
        } catch(Exception e) {
            log.error(e.getMessage(), e);
        }
    }

}
