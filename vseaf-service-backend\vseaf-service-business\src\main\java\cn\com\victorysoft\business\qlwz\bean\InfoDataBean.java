package cn.com.victorysoft.business.qlwz.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "门户信息", description = "门户信息")
public class InfoDataBean {
    private String infoId;
    private String infoType;
    private String infoTitle;
    private String infoNotes;
    private String processStatus;
    private String infoStatus;
    private String publishTime;
    private String operatorId;
    private String operatorName;
    private String operatororgId;
    private String operatororgName;
    private String operatTime;
    private String monitoeId;
    private String userId;
    private String userName;
    private String orgId;
    private String orgName;
    private String status;
    private String oponion;
    private String taskTime;
    private List processStatusArray;
    private Integer glyth = 0;//管理员退回
    private String id;//文件上传的主键id
    private String fileType;//文件类型
    private String fileName;//视频名称

}
