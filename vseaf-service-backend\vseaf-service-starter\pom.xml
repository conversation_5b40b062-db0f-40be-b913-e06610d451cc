<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>cn.com.victorysoft</groupId>
	<artifactId>vseaf-service-starter</artifactId>
	<packaging>jar</packaging>

	<parent>
		<groupId>cn.com.victorysoft</groupId>
		<artifactId>vseaf-service-backend</artifactId>
		<version>1.0-SNAPSHOT</version>
	</parent>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
	</properties>

	<dependencies>
		<!-- mysql权限 -->
		<dependency>
			<groupId>cn.com.victorysoft</groupId>
			<artifactId>vseaf-component-authm-client</artifactId>
			<version>1.1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>cn.com.victorysoft</groupId>
			<artifactId>vseaf-spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.com.victorysoft</groupId>
			<artifactId>vseaf-spring-boot-starter-security-authm-client</artifactId>
			<version>5.1.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>cn.com.victorysoft</groupId>
			<artifactId>vseaf-spring-boot-starter-web</artifactId>
		</dependency>
		<!--<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>-->
		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-jasper</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
		</dependency>

		<!--热部署 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<optional>true</optional>
		</dependency>
		<!--热部署 -->
		<dependency>
			<groupId>com.oracle.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
			<version>12.2.0.1</version>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>8.0.13</version>
		</dependency>
		<dependency>
			<groupId>cn.com.victorysoft</groupId>
			<artifactId>vseaf-service-business</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>org.mybatis.generator</groupId>
			<artifactId>mybatis-generator-core</artifactId>
			<version>1.4.0</version>
		</dependency>


		<dependency>
			<groupId>cn.com.victorysoft</groupId>
			<artifactId>vseaf-component-db</artifactId>
			<!-- 当前版本4.4.1 -->
			<version>4.4.1</version>
		</dependency>


	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
