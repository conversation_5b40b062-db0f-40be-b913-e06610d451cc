package cn.com.victorysoft.business.zsjgl.service;

import cn.com.victorysoft.business.zsjgl.bean.Dmlb;
import cn.com.victorysoft.business.zsjgl.bean.DmlbMx;
import cn.com.victorysoft.vseaf.core.repository.MybatisRepository;
import cn.com.victorysoft.vseaf.core.web.http.JsonMessage;
import cn.com.victorysoft.vseaf.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
public class ZsjglService {

    @Resource
    private MybatisRepository mybatisRepository;

    private String nat = "cn.com.victorysoft.business.zsjgl.dao.ZsjglMapper.";
    /**
     * 查询主数据列表信息
     * @return JsonMessage
     */
    public JsonMessage queryZsjList(Dmlb dmlb) {
        List<Dmlb> objects = mybatisRepository.selectByParams(nat + "selectPagingZsjList", dmlb);
        return new JsonMessage().success(objects);
    }

    /**
     * 查询主数据明细列表信息
     * @return JsonMessage
     */
    public JsonMessage queryZsjMxList(DmlbMx dmlbMx) {
        List<DmlbMx> objects = mybatisRepository.selectByParams(nat + "queryZsjMxList", dmlbMx);
        return new JsonMessage().success(objects);
    }

    /**
     * 删除主数据明细
     * @return JsonMessage
     */
    public JsonMessage delectData(Map map){
        String dmmxid = StringUtil.null2blank(map.get("dmmxid"));
        mybatisRepository.delete(nat+"delectData", dmmxid);
        return new JsonMessage().success(1);
    }

    /**
     * 主数据新增
     * @return JsonMessage
     */
    @Transactional
    public JsonMessage insertData(List<Dmlb> list) {
        for (Dmlb dmlb: list) {
            List<Dmlb> objects = mybatisRepository.selectByParams(nat+"queryTjList", dmlb);
            if (objects.size() == 0){
                mybatisRepository.insert(nat+"insertData", dmlb);
            }else {
                return new JsonMessage().failure(dmlb.getLbid() + "编号已存在");
            }
        }
        return new JsonMessage().success(1);
    }

    /**
     * 主数据明细新增
     * @return JsonMessage
     */
    @Transactional
    public JsonMessage insertMxData(List<DmlbMx> list) {
        for (DmlbMx dmlbMx: list) {
            List<DmlbMx> objects = mybatisRepository.selectByParams(nat+"queryZsjTjMxList", dmlbMx);
            if (objects.size() == 0){
                mybatisRepository.insert(nat+"insertMxData", dmlbMx);
            }else {
                mybatisRepository.insert(nat+"insertMxData", dmlbMx);
//                return new JsonMessage().failure(dmlbMx.getCode() + "编号已存在");
            }
        }
        return new JsonMessage().success(1);
    }

    /**
     * 主数据更新
     *@return JsonMessage
     * */
    public JsonMessage updateZ(List<Dmlb> list){
        for (Dmlb dmlb : list){
            mybatisRepository.update(nat+"updateZ", dmlb);
        }
        return new JsonMessage().success(1);
    }

    /**
     * 主数据明细更新
     *@return JsonMessage
     * */
    public JsonMessage updateMx(List<DmlbMx> list){
        for (DmlbMx dmlbMx : list){
            mybatisRepository.update(nat+"updateMx", dmlbMx);
        }
        return new JsonMessage().success(1);
    }

    /**
     * 删除
     * @return JsonMessage
     */
    @Transactional
    public JsonMessage delectAll(Map map){
        String lbid = StringUtil.null2blank(map.get("lbid"));

        //因为orcale的机制，必须先删除所关联子表的数据
        mybatisRepository.delete(nat+"delectMx", lbid);
        mybatisRepository.delete(nat+"delectZ", lbid);
        return new JsonMessage().success(1);
    }

}
