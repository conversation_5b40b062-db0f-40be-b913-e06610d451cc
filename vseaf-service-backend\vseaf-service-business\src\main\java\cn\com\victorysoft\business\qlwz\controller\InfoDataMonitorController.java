package cn.com.victorysoft.business.qlwz.controller;

import cn.com.victorysoft.business.qlwz.bean.InfoDataBean;
import cn.com.victorysoft.business.qlwz.bean.InfoDataMonitorBean;
import cn.com.victorysoft.business.qlwz.service.InfoDataMonitorService;
import cn.com.victorysoft.vseaf.core.web.http.JsonMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/qlwzsh")
@Api(tags = "qlwzsh")
public class InfoDataMonitorController {
    @Autowired
    InfoDataMonitorService infoDataMonitorService;
    /**
     * 保存审核意见
     */
    @PostMapping("/saveInfoDataMonitor")
    @ApiOperation(value = "保存审核意见", notes = "保存审核意见")
    public JsonMessage saveInfoDataMonitor(@RequestBody InfoDataBean infoDataBean){
        return infoDataMonitorService.saveInfoDataMonitor(infoDataBean);
    }

}
