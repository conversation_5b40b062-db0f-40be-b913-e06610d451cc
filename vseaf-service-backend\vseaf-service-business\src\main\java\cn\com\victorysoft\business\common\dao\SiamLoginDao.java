package cn.com.victorysoft.business.common.dao;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * SiamLoginDao
 * 定义接口
 */
@Repository
public interface SiamLoginDao {
	/************** 查询语句 **************/
	List<Map<String, Object>> queryUserListByLdap(String uid);
	List<Map<String, Object>> queryUserListByCard(String uid);
    List<Map<String, Object>> queryUserLocalRoleOrgList(String userLoginName);

}
