package cn.com.victorysoft.business.common.dao;

import cn.com.victorysoft.business.common.bean.DepartmentBean;
import cn.com.victorysoft.business.common.bean.DmbBean;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface DmbMapper {


    /**
     * 查询代码信息
     * @return JsonMessage
     * <AUTHOR>
     * @Date 2021/10/09 09:46
     * @param bean
     */
    List<DmbBean> queryDmList(DmbBean bean);


    /**
     * 查询组织机构
     * @return JsonMessage
     * <AUTHOR>
     * @Date 2021/10/09 09:46
     * @param bean
     */
    List<DepartmentBean> queryOrgList(DepartmentBean bean);

}
