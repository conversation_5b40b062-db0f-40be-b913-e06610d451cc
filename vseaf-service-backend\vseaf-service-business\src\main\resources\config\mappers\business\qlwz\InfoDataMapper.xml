<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.victorysoft.business.qlwz.dao.InfoDataMapper">
    <!-- 查询信息门户数据,查询的时候同时将审核信息的相关信息也查询出来，而信息门户的主键是审核信息的外键-->
    <select id="queryInfoDataList"
            parameterType="cn.com.victorysoft.business.qlwz.bean.InfoDataBean"
            resultType="cn.com.victorysoft.business.qlwz.bean.InfoDataBean">
        SELECT
        A.INFOID,
        A.INFO_TYPE,
        A.INFO_TITLE,
        A.INFO_NOTES,
        A.PROCESS_STATUS,
        A.INFO_STATUS,
        TO_CHAR(A.PUBLISH_TIME, 'YYYY-MM-DD HH24:MI:SS') AS PUBLISH_TIME,
        A.OPERATORID,
        A.OPERATORNAME,
        A.OPERATORORGID,
        A.OPERATORORGNAME,
        TO_CHAR(A.OPERATTIME, 'YYYY-MM-DD HH24:MI:SS') AS OPERATTIME,
        B.MONITOEID,
        B.USERID,
        B.USERNAME,
        B.ORGID,
        B.ORGNAME,
        B.STATUS,
        B.OPONION,
        TO_CHAR(B.TASKTIME, 'YYYY-MM-DD HH24:MI:SS') AS TASKTIME
        FROM INFO_DATA A
        LEFT JOIN (
        SELECT B1.INFOID, B1.MONITOEID, B1.USERID, B1.USERNAME, B1.ORGID, B1.ORGNAME,
        B1.STATUS, B1.OPONION, B1.TASKTIME
        FROM INFO_DATA_MONITOR B1
        INNER JOIN (
        SELECT INFOID, MAX(TASKTIME) AS MAX_TASKTIME
        FROM INFO_DATA_MONITOR
        GROUP BY INFOID
        ) B2 ON B1.INFOID = B2.INFOID AND B1.TASKTIME = B2.MAX_TASKTIME
        ) B ON A.INFOID = B.INFOID
        <where>
            <if test="isAudit != null and isAudit == 1">
                <if test="processStatus != null and processStatus == 1">
                    A.OPERATORORGID IN (SELECT S.SHDWID FROM INFO_SHRPZ S WHERE S.SHRID = #{userId})
                </if>
            </if>
            <if test="infoStatus != null and infoStatus != ''">
                A.INFO_STATUS = #{infoStatus}
            </if>
            <if test="infoTitle != null and infoTitle != ''">
                AND A.INFO_TITLE LIKE CONCAT(CONCAT('%', #{infoTitle}), '%')
            </if>
            <if test="operatorId != null and operatorId != ''">
                AND A.OPERATORID = #{operatorId}
            </if>
            <if test="infoType != null and infoType != ''">
                AND A.INFO_TYPE = #{infoType}
            </if>
            <if test="processStatus != null and processStatus != ''">
                AND A.PROCESS_STATUS = #{processStatus}
            </if>
            <if test="processStatusArray != null">
                AND A.PROCESS_STATUS IN
                <foreach collection="processStatusArray" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>

            </if>
        </where>
        ORDER BY A.OPERATTIME DESC
    </select>
    <!--    保存门户信息-->
    <update id="saveInfoData" parameterType="cn.com.victorysoft.business.qlwz.bean.InfoDataBean">
        MERGE INTO INFO_DATA t
        USING (SELECT
        #{infoId} AS INFOID,
        #{infoType} AS INFO_TYPE,
        #{infoTitle} AS INFO_TITLE,
        #{infoNotes,jdbcType=VARCHAR} AS INFO_NOTES,
        #{processStatus,jdbcType=VARCHAR} AS PROCESS_STATUS,
        #{infoStatus} AS INFO_STATUS,
        <choose>
            <when test="infoStatus == 0">
                NULL AS PUBLISH_TIME
            </when>
            <when test="infoStatus == 1">
                (SELECT PUBLISH_TIME FROM INFO_DATA WHERE INFOID = #{infoId}) AS PUBLISH_TIME
            </when>
            <otherwise>
                #{publishTime,jdbcType=DATE} AS PUBLISH_TIME
            </otherwise>
        </choose>
        ,
        #{operatorId} AS OPERATORID,
        #{operatorName} AS OPERATORNAME,
        #{operatororgId} AS OPERATORORGID,
        #{operatororgName} AS OPERATORORGNAME,
        SYSDATE AS OPERATTIME
        FROM dual) s
        ON (t.INFOID = s.INFOID)
        WHEN MATCHED THEN
        UPDATE SET
        t.INFO_TYPE = s.INFO_TYPE,
        t.INFO_TITLE = s.INFO_TITLE,
        t.INFO_NOTES = s.INFO_NOTES,
        t.PROCESS_STATUS = s.PROCESS_STATUS,
        t.INFO_STATUS = s.INFO_STATUS,
        t.PUBLISH_TIME = CASE
        WHEN s.INFO_STATUS = 1 THEN
        CASE
        WHEN (SELECT PUBLISH_TIME FROM INFO_DATA WHERE INFOID = t.INFOID) IS NULL THEN SYSDATE
        ELSE (SELECT PUBLISH_TIME FROM INFO_DATA WHERE INFOID = t.INFOID)
        END
        ELSE NULL
        END,
        t.OPERATORID = s.OPERATORID,
        t.OPERATORNAME = s.OPERATORNAME,
        t.OPERATORORGID = s.OPERATORORGID,
        t.OPERATORORGNAME = s.OPERATORORGNAME
        WHEN NOT MATCHED THEN
        INSERT (INFOID, INFO_TYPE, INFO_TITLE, INFO_NOTES, PROCESS_STATUS, INFO_STATUS, PUBLISH_TIME,
        OPERATORID, OPERATORNAME, OPERATORORGID, OPERATORORGNAME, OPERATTIME)
        VALUES (s.INFOID, s.INFO_TYPE, s.INFO_TITLE, s.INFO_NOTES, s.PROCESS_STATUS, s.INFO_STATUS,
        <choose>
            <when test="infoStatus == 1">
                SYSDATE
            </when>
            <otherwise>
                NULL
            </otherwise>
        </choose>
        ,
        s.OPERATORID, s.OPERATORNAME, s.OPERATORORGID, s.OPERATORORGNAME, SYSDATE)
    </update>
    <!--    删除门户信息-->
    <delete id="deleteInfoData" parameterType="cn.com.victorysoft.business.qlwz.bean.InfoDataBean">
        delete
        from INFO_DATA
        where INFOID = #{infoId}
    </delete>
    <!--    根据主键infoId查询门户信息-->
    <select id="queryInfoDataById" parameterType="java.lang.String"
            resultType="cn.com.victorysoft.business.qlwz.bean.InfoDataBean">
        select T.INFOID,
               T.INFO_TYPE,
               T.INFO_TITLE,
               T.INFO_NOTES,
               T.PROCESS_STATUS,
               T.INFO_STATUS,
               TO_CHAR(T.PUBLISH_TIME, 'YYYY-MM-DD HH24:MI:SS') AS PUBLISH_TIME,
               T.OPERATORID,
               T.OPERATORNAME,
               T.OPERATORORGID,
               T.OPERATORORGNAME,
               TO_CHAR(T.OPERATTIME, 'YYYY-MM-DD HH24:MI:SS')   AS OPERATTIME
            from INFO_DATA T
              where T.INFOID = #{infoId}
    </select>
    <!--    查询纪发课堂的前三条最新数据-->
    <select id="queryInfoData" resultType="cn.com.victorysoft.business.qlwz.bean.InfoDataBean"
            parameterType="cn.com.victorysoft.business.qlwz.bean.InfoDataBean">
        SELECT * FROM (
        select T.INFOID, T.INFO_TITLE, T.PUBLISH_TIME, F.ID, F.FILE_TYPE, ROW_NUMBER() OVER(ORDER BY PUBLISH_TIME DESC) AS row_num
        from INFO_DATA T
        LEFT JOIN APP_FILESUPLOAD F ON F.OPERATION_ID = T.INFOID AND F.STANDBYFIELD0 = 'JFKTimg'  and isvalid = '1'
        where info_type = 'JFKT' AND INFO_STATUS = '1'
        ORDER BY T.PUBLISH_TIME DESC
        )
        WHERE row_num &lt;4
    </select>
    <!--    查询纪发课堂提交的所有视频的列表-->
    <select id="queryInfoList" resultType="cn.com.victorysoft.business.qlwz.bean.InfoDataBean">
        SELECT T.INFOID,
               T.INFO_TITLE,
               TO_CHAR(T.PUBLISH_TIME, 'YYYY-MM-DD HH24:MI:SS') AS PUBLISH_TIME,
               F.ID,
               F.FILE_TYPE,
               F.FILENAME
        FROM (SELECT *
              FROM INFO_DATA
              WHERE INFO_TYPE = 'JFKT'
                AND INFO_STATUS = '1'
              ORDER BY PUBLISH_TIME DESC) T
                 RIGHT JOIN (SELECT *
                             FROM APP_FILESUPLOAD
                             WHERE STANDBYFIELD0 = 'JFKTvideo'
                               AND ISVALID = '1') F
                            ON F.OPERATION_ID = T.INFOID
        <where>
           <if test="fileName != null and fileName != ''">F.FILENAME like CONCAT(CONCAT('%', #{fileName}), '%')</if>
        and T.INFOID IS NOT NULL
        </where>
        ORDER BY T.PUBLISH_TIME DESC
    </select>
<!--    通过上传文件的业务id查询出类型是视频类型的信息-->
    <select id="queryVideo" resultType="cn.com.victorysoft.business.qlwz.bean.InfoDataBean"
            parameterType="cn.com.victorysoft.business.qlwz.bean.InfoDataBean">
        SELECT T.ID
        FROM APP_FILESUPLOAD T
        WHERE T.OPERATION_ID = #{infoId} AND T.FILE_TYPE = '.mp4' and isvalid = '1'
    </select>

    <!--查询网站信息列表-->
    <select id="queryInfoWebList" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT T.INFOID,
               T.INFO_TYPE,
               T.INFO_TITLE,
               T.INFO_NOTES,
               TO_CHAR(T.PUBLISH_TIME, 'yyyy-MM-dd') PUBLISH_TIME,
               T.OPERATORID,
               T.OPERATORNAME,
               T.OPERATORORGID,
               T.OPERATORORGNAME
          FROM INFO_DATA T
         WHERE T.INFO_TYPE = #{type}
        <if test="title != null and title != ''">
            AND T.INFO_TITLE LIKE '%' || #{title} || '%'
        </if>
           AND T.INFO_STATUS = '1'
         ORDER BY T.PUBLISH_TIME DESC
    </select>

    <select id="queryInfoWebOne" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        SELECT T.INFOID,
               T.INFO_TYPE,
               T.INFO_TITLE,
               T.INFO_NOTES,
               TO_CHAR(T.PUBLISH_TIME, 'yyyy-MM-dd') PUBLISH_TIME,
               T.OPERATORID,
               T.OPERATORNAME,
               T.OPERATORORGID,
               T.OPERATORORGNAME
        FROM INFO_DATA T
        WHERE T.INFOID = #{id}
    </select>
</mapper>