package cn.com.victorysoft.business.common.service;

import cn.com.victorysoft.business.common.dao.AccesslogMapper;
import cn.com.victorysoft.vseaf.core.repository.MybatisRepository;
import cn.com.victorysoft.vseaf.core.web.http.JsonMessage;
import cn.com.victorysoft.vseaf.util.IDUtil;
import cn.com.victorysoft.vseaf.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统日志 service
 * @ClassName: AccesslogService
 * @DATE: 2023/05/23
 * <AUTHOR>
 */
@Service
public class AccesslogService {

    @Resource
    private MybatisRepository mybatisRepository;

    @Autowired
    private  HttpServletRequest request;

    @Autowired
    private AccesslogMapper accesslogMapper;


    /**
     * 存储日志信息
     * AREAID 应用ID
     * TENANTID 租户ID
     * LOGCONTENT 访问的菜单名
     * METHOD 路由
     * PARAMES 登陆人姓名
     * DEVICEIP 访问IP
     * LOGTYPE 日志类型 登出（LOGOUT） 访问（SELECT）
     * OPRATETYPE 操作类型 登出（LOGOUT） 访问（SELECT）
     * OPRATEUSER 登陆人ID
     * OPRATERESULT 操作结果 成功（1）
     */
    public JsonMessage insertLog(Map<String, Object> params) {
        //获取访问者ip
        String ip = getIp();
        System.out.println("访问ip： " + ip);
        //定义存储参数
        Map map = new HashMap();
        map.put("AREAID", StringUtil.null2blank(params.get("AREAID")));
        map.put("TENANTID", StringUtil.null2blank(params.get("TENANTID")));
        map.put("LOGCONTENT", StringUtil.null2blank(params.get("LOGCONTENT")));
        map.put("METHOD", StringUtil.null2blank(params.get("METHOD")));
        map.put("PARAMES", StringUtil.null2blank(params.get("PARAMES")));
        map.put("DEVICEIP", ip);
        map.put("LOGTYPE", StringUtil.null2blank(params.get("LOGTYPE")));
        map.put("OPRATETYPE", StringUtil.null2blank(params.get("OPRATETYPE")));
        map.put("OPRATEUSER", StringUtil.null2blank(params.get("OPRATEUSER")));
        map.put("OPRATERESULT", StringUtil.null2blank(params.get("OPRATERESULT")));
        //存储日志
        int result = accesslogMapper.insertLog(map);
        return new JsonMessage().success(result);
    }

    /**
     * 获取访问者ip
     */
    public String getIp(){
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if ("127.0.0.1".equals(ip) || "0:0:0:0:0:0:0:1".equals(ip)) {
            try {
                ip = InetAddress.getLocalHost().getHostAddress();
            } catch (Exception e) {
                ip = "";
            }
        }
        return ip;
    }

}
