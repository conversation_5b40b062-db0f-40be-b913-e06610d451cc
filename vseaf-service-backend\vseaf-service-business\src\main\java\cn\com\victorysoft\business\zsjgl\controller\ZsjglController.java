package cn.com.victorysoft.business.zsjgl.controller;

import cn.com.victorysoft.business.zsjgl.bean.Dmlb;
import cn.com.victorysoft.business.zsjgl.bean.DmlbMx;
import cn.com.victorysoft.business.zsjgl.service.ZsjglService;
import cn.com.victorysoft.vseaf.core.web.http.JsonMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 工具类
 * <AUTHOR> @date
 * @return
 */
@RestController
@Api(tags = "ZsjController")
@RequestMapping("/ZsjController")
public class ZsjglController {

    @Autowired
    ZsjglService zsjglService;

    /**
     * 方法
     * <AUTHOR> @date
     * @return
     */
    @PostMapping("/queryZsjList")
    @ApiOperation(value = "queryZsjList", notes = "查询主数据信息")
    public JsonMessage queryZsjList(Dmlb dmlb) {
        return zsjglService.queryZsjList(dmlb);
    }

    /**
     * 方法
     * <AUTHOR> @date
     * @return
     */
    @PostMapping("/queryZsjMxList")
    @ApiOperation(value = "queryZsjMxList", notes = "查询主数据明细信息")
    public JsonMessage queryZsjMxList(@RequestBody DmlbMx dmlbMx) {
        return zsjglService.queryZsjMxList(dmlbMx);
    }

    /**
     * 方法
     * <AUTHOR> @date
     * @return
     */
    @PostMapping("/delectData")
    @ApiOperation(value = "delectData", notes = "删除明细 map存放要删除数据的code")
    public JsonMessage delectData(@RequestBody Map map) {
        return zsjglService.delectData(map);
    }

    /**
     * 方法
     * <AUTHOR> @date
     * @return
     */
    @PostMapping("/insertData")
    @ApiOperation(value = "insertData", notes = "主数据增加")
    public JsonMessage insertData(@RequestBody List<Dmlb> list){
        return zsjglService.insertData(list);
    }

    @PostMapping("/insertMxData")
    @ApiOperation(value = "insertMxData", notes = "主数据明细增加")
    public JsonMessage insertMxData(@RequestBody List<DmlbMx> list){
        return zsjglService.insertMxData(list);
    }

    @PostMapping("/updateZ")
    @ApiOperation(value = "updateZ", notes = "主数据更新")
    public JsonMessage updateZ(@RequestBody List<Dmlb> list){
        return zsjglService.updateZ(list);
    }

    @PostMapping("/updateMx")
    @ApiOperation(value = "updateMx", notes = "主数据明细更新")
    public JsonMessage updateMx(@RequestBody List<DmlbMx> list){
        return zsjglService.updateMx(list);
    }

    @PostMapping("/delectAll")
    @ApiOperation(value = "delectAll", notes = "删除主数据已经所关联字表明细")
    public JsonMessage delectAll(@RequestBody Map map){
        return zsjglService.delectAll(map);
    }

}
