package cn.com.victorysoft.business.shwh.controller;

import cn.com.victorysoft.business.shwh.bean.ShwhBean;
import cn.com.victorysoft.business.shwh.service.ShwhService;
import cn.com.victorysoft.vseaf.core.web.http.JsonMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 审核维护 controller
 * @package: cn.com.victorysoft.business.shwh.controller
 * @className: ShwhController
 * @author: kong<PERSON><PERSON>
 * @date: 2025/7/22 16:23
 */
@RestController
@RequestMapping("/shwhController")
@Api(tags = "shwhController")
public class ShwhController {

    @Autowired
    private ShwhService shwhService;

    /**
     * 查询审核人信息列表
     * @return JsonMessage
     * <AUTHOR>
     * @param shwhBean
     */
    @PostMapping("/selectShwhList")
    @ApiOperation(value = "查询审核人信息列表", notes = "selectShwhList")
    public JsonMessage selectShwhList(@RequestBody ShwhBean shwhBean){
        return shwhService.selectShwhList(shwhBean);
    }

    /**
     * 删除单个审核人信息
     * @return JsonMessage
     * <AUTHOR>
     * @param shwhBean
     */
    @PostMapping("/deleteOneShwh")
    @ApiOperation(value = "删除单个审核人信息", notes = "deleteOneShwh")
    public JsonMessage deleteOneShwh(@RequestBody ShwhBean shwhBean) {
        return shwhService.deleteOneShwh(shwhBean);
    }

    /**
     * 保存审核人信息
     * @return JsonMessage
     * <AUTHOR>
     * @param shwhBean
     */
    @PostMapping("/insertShwhList")
    @ApiOperation(value = "保存审核人信息", notes = "insertShwhList")
    public JsonMessage insertShwhList(@RequestBody ShwhBean shwhBean){
        return shwhService.insertShwhList(shwhBean);
    }

    /**
     * 查询审人员选择列表
     * @return JsonMessage
     * <AUTHOR>
     * @param shwhBean
     */
    @PostMapping("/selectUserSelectInfo")
    @ApiOperation(value = "查询审人员选择列表", notes = "selectUserSelectInfo")
    public JsonMessage selectUserSelectInfo(@RequestBody ShwhBean shwhBean){
        return shwhService.selectUserSelectInfo(shwhBean);
    }


}
