package cn.com.victorysoft.business.common.util;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.text.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/*
胜软综合util

 */

public class VsUtil {

    private static final double DOUBLEDATA1=0.001D;

    private static final double DOUBLEDATA5=0.5D;

    private static final int SHORTDATA2=2;

    private static final int SHORTDATA4=4;
    private static final int SHORTDATA5=5;

    private static final int SHORTDATA6=6;
    private static final int SHORTDATA7=7;
    private static final int SHORTDATA8=8;
    private static final int SHORTDATA10=10;
    private static final int SHORTDATA20=20;
    private static final int SHORTDATA11=11;

    public static final String CLASSNAME = "PnM Utility Bean";

    private VsUtil() {
    }
    public  static boolean isEqual(double a, double b) {
        if (Double.isNaN(a) || Double.isNaN(b) || Double.isInfinite(a)
                || Double.isInfinite(b)) {
            return false;
        }
        return (a - b) < DOUBLEDATA1;
    }
    /**
     * 将0转换为&nbsp;
     *
     * @param value
     *            double 输入数值
     * @return String 如果为零，返回&nbsp；否则，返回原数值[字符串类型]
     */
    public static String zero2nbsp(double value) {
        String result = "";
        if (isEqual(value, 0F))
            result = "&nbsp";
        else
            result = String.valueOf(value);
        return result;
    }
/*
* 通过request获取所有参数
* */
    public static Map getMap(HttpServletRequest request) {
        Map map = new HashMap();
        Enumeration pNames = request.getParameterNames();
        while (pNames.hasMoreElements()) {
            String name = (String) pNames.nextElement();
            if (request.getParameter(name) != null) {
                map.put(name, VsUtil.null2str(request.getParameter(name)));
            }
        }
        return map;
    }

    //
    /**
     * 传入日期格式参数 如果输入字符串为空，返回空串<br>
     * 如果出错，返回出错信息
     *
     * @param s
     *            输入的日期字符串
     * @return 经过x1，x2，x9
     */
    public static String getTendayPeriod(String s) {
        String result = null;
        String nian = null;
        String yue = null;
        String ri = null;
        // 处理空串
        if (s == null) {
            return new String("");
        }
        // 转换字符集
        try {// 2005-07-06
            nian = s.substring(0, SHORTDATA4);
            yue = s.substring(SHORTDATA5, SHORTDATA7);
            ri = s.substring(SHORTDATA8, SHORTDATA10);
            int day = Integer.parseInt(ri);
            if ((day <= SHORTDATA10) && (day >= 1)) {
                return (nian + yue + "X1");
            } else if ((day <= SHORTDATA20) && (day >= SHORTDATA11)) {
                return (nian + yue + "X2");
            } else {
                return (nian + yue + "X9");
            }

        }catch (Exception e) {// 捕捉错误信息
            // errorMsg += unsupportedencodingexception.toString();
            // result = new String("错误！<br>iso2gbk代码转换失败！<br>源字符串为：") + s +
            // "<br>" + e +
            // "<br>";
        }
        return result;
    }

    /**
     * 将字符串从iso编码转换为gbk编码<br>
     * 如果输入字符串为空，返回空串<br>
     * 如果出错，返回出错信息
     *
     * @param s
     *            输入的字符串
     * @return 经过转换的字符串
     */
    public static String iso2gb(String s) {
        String result = null;
        // 处理空串
        if (s == null) {
            return new String("");
        }
        // 转换字符集
        try {
            result = new String(s.getBytes("ISO-8859-1"), "GBK");
        }catch (UnsupportedEncodingException e) {// 捕捉错误信息
            // errorMsg += unsupportedencodingexception.toString();
            result = new String("错误！<br>iso2gbk代码转换失败！<br>源字符串为：") + s + "<br>"
                    + e + "<br>";
        }
        return result;
    }

    /**
     * 将字符串从gbk编码转换为iso编码<br>
     * 如果输入字符串为空，返回空串<br>
     * 如果出错，返回出错信息
     *
     * @param s
     *            输入的字符串
     * @return 经过转换的字符串
     */
    public static String gb2iso(String s) {
        String result = null;
        // 处理空串
        if (s == null) {
            return new String("");
        }
        // 转换字符集
        try {
            result = new String(s.getBytes("GBK"), "ISO-8859-1");
        }catch (UnsupportedEncodingException e) {// 捕捉错误信息
            result = new String("错误！<br>gbk2iso代码转换失败！<br>源字符串为：") + s + "<br>"
                    + e + "<br>";
        }
        return result;
    }

    /**
     * 四舍五入方法<br>
     * 解决了java语言中-1.5四舍五入变成-1的错误
     *
     * @param d
     *            输入的数值
     * @param n
     *            保留的小数位数
     * @return 经过处理后的数值<br>
     *         to do: 有效数字的位数，10.000和10在数学上的含义是不同的
     */
    public static double round(double d, int n) {
        // d*10^n
        double t = d * Math.pow(SHORTDATA10, n);
        // 四舍五入取整
        long l;
        if (t > 0)
            l = (long) (t + DOUBLEDATA5);
        else
            l = (long) (t - DOUBLEDATA5);
        // d/10^n
        t = (double) l / Math.pow(SHORTDATA10, n);

        // 为有效数字补充0

        return t;
    }

    // /* 对个位数的月份之前补零 */
    // private static String impleMonth(int month) {
    // String monthStr = new Integer(month).toString();
    // if (monthStr.length() == 1) {
    // monthStr = "0" + monthStr;
    // }
    // return monthStr;
    // }

    // /* 对个位数的日子之前补零 */
    // private static String impleDay(int day) {
    // String dayStr = new Integer(day).toString();
    // if (dayStr.length() == 1) {
    // dayStr = "0" + dayStr;
    // }
    // return dayStr;
    // }

    // /* 得到当前月的第一天的日期 */
    // public static String getMonthFirstDate() {
    // Date da = new Date();
    //
    // long dayOfMonth = da.getDate(); // 当前日期是本月第几天
    // long fromMonthFirstInMillis = (dayOfMonth - 1) * 24 * 60 * 60 * 1000; //
    // 与该月第一天相隔的毫秒数
    // da.setTime(da.getTime() - fromMonthFirstInMillis);
    // String MonthFirstDay = new Integer(da.getYear() + 1900).toString();
    //
    // MonthFirstDay = MonthFirstDay + "-" + impleMonth(da.getMonth() + 1);
    // MonthFirstDay = MonthFirstDay + "-" + impleDay(da.getDate());
    // return MonthFirstDay;
    // }

    /**
     * 将空(null)转换为空串
     *
     * @param s
     *            输入字符串
     * @return 经过处理的串
     */
    public static String null2blank(String s) {
        if ((!(s == null)) && (s.length() > 0) && (!s.equals("null")))
            return s;
        else
            return " ";
    }

    /**
     * 将空(null)转换为空串
     *
     * @param s
     *            输入字符串
     * @return 经过处理的串
     */
    public static String null2string(String s) {
        if ((!(s == null)) && (s.length() > 0) && (!s.equals("null")))
            return s;
        else
            return "";
    }

    /**
     * 将空(null)转换为空串
     *
     * @param o
     *            输入Object
     * @return 经过处理的串
     */
    public static String null2str(Object o) {
        if (o == null) {
            return "";
        } else {
            if (o.equals("") || o.equals("null")) {
                return "";
            } else {
                return o.toString();
            }
        }
    }

    /**
     * 将空(null)转换为空串
     *
     * @param o
     *            输入Object
     * @return 经过处理的串
     */
    public static String null2str(Object o, int num, String splitst) {
        if (o == null) {
            return "";
        } else {
            if (o.equals("") || o.equals("null")) {
                return "";
            } else {
                StringBuffer result = new StringBuffer("");
                int i = 0;
                for (; i < (o.toString().length() - num)
                        && o.toString().length() > num; i += num) {
                    result.append(o.toString().substring(i, i + num));
                    result.append(splitst);
                }
                result.append(o.toString().substring(i, o.toString().length()));
                result.append(splitst);
                return result.toString();
            }
        }
    }

    /**
     * 将空(null)转换为空串(&nbsp;)
     *
     * @param s
     *            输入字符串
     * @return 经过处理的串
     */
    public static String null2nbsp(String s) {
        if ((!(s == null)) && (s.length() > 0) && (!s.equals("null")))
            return s;
        else
            return "&nbsp;";
    }

    public static String null2nbsp(Object o) {
        if (o == null) {
            return "&nbsp;";
        } else {
            if (o.equals("") || o.equals("null")) {
                return "&nbsp;";
            } else {
                return o.toString();
            }
        }
    }

    /**
     * 将空(null)转换为double型字符(0.00)
     *
     * @param s
     *            输入字符串
     * @return 经过处理的串
     */
    public static String null2double(String s) {
        String ss = "";
        DecimalFormat df = new DecimalFormat("0.00");
        if ((!(s == null)) && (s.length() > 0) && (!s.equals("null"))) {
            ss = String.valueOf(df.format(Double.parseDouble(s)));
        } else {
            ss = "0.00";
        }
        return ss;
    }

    public static String null2int(String s) {
        String ss = "";
        if ((!(s == null)) && (s.length() > 0) && (!s.equals("null"))) {
            ss = String.valueOf((Integer.parseInt(s)));
        } else {
            ss = "0";
        }
        return ss;
    }

    /**
     * 字符串替换方法
     *
     * @param s
     *            输入的字符串
     * @param oldStr
     *            原字符串
     * @param newStr
     *            要替换的新字符串
     * @return 新的字符串
     */
    public static String replace(String s, String oldStr, String newStr) {
        // 检查输入数据
        if (s == null)
            return "";
        // 处理
        String ss = new String(s);
        StringBuffer sb = new StringBuffer(s);

        //
        int begin = 0, end = 0;
        begin = ss.indexOf(oldStr);
        while (begin > -1) {
            end = begin + oldStr.length();
            sb = sb.replace(begin, end, newStr);
            ss = new String(sb);
            begin = ss.indexOf(oldStr, begin + newStr.length());
        }
        return ss;
    }

    /**
     * 将字符串格式化为Html格式
     *
     * @param s
     *            输入字符串
     * @return 经过格式化的字符串
     */
    public static String htmlFormat(String s) {
        // 检查输入数据
        if (s == null)
            return "";

        // 处理
        String ss = new String(s);
        // 将两个西文空格转换成一个汉字空格
        ss = replace(ss, "  ", "　");
        // 将一个西文空格转换成一个汉字空格
        // ss = replace(ss, " ", " ");
        // 将多个汉字空格转换成一个汉字空格
        ss = replace(ss, "　　", "　");
        ss = replace(ss, "　　", "　");
        // 去掉将回车后面的空格
        ss = replace(ss, "\n　　", "\n");
        ss = replace(ss, "\n　", "\n");
        // 在回车后面加上空格
        ss = replace(ss, "\n", "\n　　");
        // 将回车符转换成html回车符
        ss = replace(ss, "\n", "<br>");
        // 返回数据
        return ss;
    }

    /**
     * 时间加法
     *
     * @param datetime
     *            String 输入时间
     * @param datetimeFormat
     *            String 时间格式
     * @param field
     *            int 跨度单位，年-Calendar.Date，月-Calendar.Month，日-Calendar.Year
     * @param number
     *            int 数量
     * @return String 与输入格式相同的时间
     */
    public static String dateAdd(String datetime, String datetimeFormat,
                                 int field, int number) {
        // 检查输入数据
        if (datetime == null || datetime.length() == 0)
            return "";
        String result = "";
        try {
            // 格式化对象
            SimpleDateFormat format = new SimpleDateFormat(datetimeFormat);
            // 转换为日期
            Date inputDate = format.parse(datetime, new ParsePosition(0));
            // 日历对象
            Calendar cal = Calendar.getInstance();
            // 设置为输入日期
            cal.setTime(inputDate);
            // 日期加法
            cal.add(field, number);
            // 日期结果
            Date resultDate = cal.getTime();
            // 格式化为字符串
            StringBuffer resultValue = format.format(resultDate,
                    new StringBuffer(), new FieldPosition(0));
            // 返回值
            result = resultValue.toString();
        } catch (Exception e) {
            // Debug.println("Error occurs while translate date format.");
            // Debug.print(e);
        }

        // 返回
        return (result);
    }

    /**
     * 日期加法
     *
     * @param inDate
     *            输入日期字符串
     * @param days
     *            天数
     * @param dateFormat
     *            日期格式串
     * @return 日期字符串
     *         <p>
     *         2003年12月31日：
     *         <p>
     *         更改了日期加法的jdk算法错误（根据彭传璠将军的代码修改）
     */
    public static String dateAdd(String inDate, String dateFormat, int days) {
        // 检查输入数据
        if (inDate == null || inDate.length() == 0)
            return "";
        // 格式化对象
        SimpleDateFormat format = new SimpleDateFormat(dateFormat);
        // 转换为日期
        Date inputDate = format.parse(inDate, new ParsePosition(0));
        // 日历对象
        Calendar cal = Calendar.getInstance();
        // 设置为输入日期
        cal.setTime(inputDate);
        // 日期加法
        cal.add(Calendar.DATE, days);
        // 日期结果
        Date resultDate = cal.getTime();
        // 格式化为字符串
        StringBuffer result = format.format(resultDate, new StringBuffer(),
                new FieldPosition(0));
        // 返回
        return (result.toString());
    }

    /**
     * 日期加法
     *
     * @param inDate
     *            输入日期字符串
     * @param days
     *            天数
     * @param dateFormat
     *            日期格式串
     * @return 日期字符串
     *         <p>
     *         2003年12月31日：
     *         <p>
     *         更改了日期加法的jdk算法错误（根据彭传璠将军的代码修改）
     */
    public static String dateAdd(String inDate, int days, String dateFormat) {
        // 检查输入数据
        if (inDate == null || inDate.length() == 0)
            return "";
        // ========= 以下代码有问题，输入天数大了结果就不对，已经废弃 ===================
        // //处理
        // SimpleDateFormat format = new SimpleDateFormat(dateFormat);
        // Date parsedDate = format.parse(inDate, new ParsePosition(0));
        // return format.format(new Date(parsedDate.getTime() +
        // (long) (days * 3600 * 24 * 1000)));
        // ========== 以上代码有问题，已经废弃 ==========================

        // ========== 以下代码由张义文改写于彭传璠将军的代码，2003年12月31日
        // ===========================
        // 格式化对象
        SimpleDateFormat format = new SimpleDateFormat(dateFormat);
        // 转换为日期
        Date inputDate = format.parse(inDate, new ParsePosition(0));
        // 日历对象
        Calendar cal = Calendar.getInstance();
        // 设置为输入日期
        cal.setTime(inputDate);
        // 日期加法
        cal.add(Calendar.DATE, days);
        // 日期结果
        Date resultDate = cal.getTime();
        // 格式化为字符串
        StringBuffer result = format.format(resultDate, new StringBuffer(),
                new FieldPosition(0));
        // 返回
        return (result.toString());
    }

    /**
     * 日期格式化
     *
     * @param inDate
     *            输入日期字符串
     * @param inFormat
     *            输入日期格式
     * @param outFormat
     *            输出日期格式
     * @return 输出日期字符串
     */
    public static String dateFormat(String inDate, String inFormat,
                                    String outFormat) {
        String result = "";
        // 检查输入数据
        if (inDate == null || inDate.length() == 0)
            return result;
        // 处理
        try {
            SimpleDateFormat format = new SimpleDateFormat(inFormat);
            Date parsedDate = format.parse(inDate, new ParsePosition(0));
            format.applyPattern(outFormat);
            result = format.format(new Date(parsedDate.getTime()));
        } catch (Exception e) {
            // Debug.println("格式转换失败！");
        }

        return result;
    }

    /**
     * 计算若干天以前为哪一天
     *
     * @param date
     *            String 输入日期
     * @param format
     *            String 输入日期的格式串
     * @param days
     *            int 天数
     * @return String 返回日期，格式为“yyyy年M月d日”
     */
    public static String daysBefore(String date, String format, int days) {
        String result = dateFormat(dateAdd(date, -days, format), format,
                "yyyy年M月d日");
        return result;
    }

    public static String daysBefores(String date, String format, int days) {
        String result = dateFormat(dateAdd(date, -days, format), format,
                "yyyy-MM-dd");
        return result;
    }

    /**
     * 计算若干月以前的日期
     *
     * @param monthDate
     *            String 年月
     * @param monthFormat
     *            String 年月格式
     * @param months
     *            int 月数
     * @return String 返回日期，格式为“yyyy年M月”
     */
    public static String monthsBefore(String monthDate, String monthFormat,
                                      int months) {
        // 检查输入数据
        if (monthDate == null || monthDate.length() == 0)
            return "";

        String result = dateFormat(
                dateAdd(monthDate, monthFormat, Calendar.MONTH, -months),
                monthFormat, "yyyy年M月");
        // 返回结果
        return result;
    }

    /**
     * 计算若干年以前的本月
     *
     * @param yearDate
     *            String 日期
     * @param format
     *            String 格式
     * @param years
     *            int 年数
     * @return String 返回年月，格式为：“yyyy年M月”
     */
    public static String yearsBefore(String yearDate, String format, int years) {
        String result = "";
        try {
            result = dateFormat(
                    dateAdd(yearDate, format, Calendar.YEAR, -years), format,
                    "yyyy年M月");
        } catch (Exception e) {
            // Debug.println("年月计算失败!");
        }
        return result;
    }

    /**
     * 计算输入日期为一年中的第几天
     *
     * @param inDate
     *            String 输入日期字符串
     * @param dateFormat
     *            String 输入日期格式
     * @return int 第几天序号
     */
    public static int dayOfYear(String inDate, String dateFormat) {
        int result = 0;

        // 格式化对象
        SimpleDateFormat format = new SimpleDateFormat(dateFormat);
        // 将输入日期字符串转换为日期格式字符串
        Date inputDate = format.parse(inDate, new ParsePosition(0));
        // 日历对象
        Calendar cal = Calendar.getInstance(Locale.CHINA);
        // 设置为输入日期
        cal.setTime(inputDate);

        // 计算日期的在一年中的序号
        result = cal.get(Calendar.DAY_OF_YEAR);

        // 返回数据
        return result;
    }


    /**
     * 计算从年初到某月某旬的天数
     *
     * @param tendays
     *            String 旬度，格式为：上旬-yyyyMMX1，中旬-yyyyMMX2，下旬-yyyyMMX9
     * @return int 天数
     */
    public static int daysOfTendays(String tendays) {
        int result = 0;
        // 年月
        String yearMonth = tendays.substring(0, SHORTDATA6);
        // 日期格式
        String format = "yyyyMMdd";
        //
        String day = tendays.substring(SHORTDATA7);
        if (day.equals("1")) { // 当月10日的天数
            result = dayOfYear(yearMonth + "10", format);
        } else if (day.equals("2")) { // 当月20日的天数
            result = dayOfYear(yearMonth + "20", format);
        } else { // 下月1日的天数 - 1
            String tempDate = dateAdd(yearMonth + "01", format, Calendar.MONTH,
                    1);
            result = dayOfYear(tempDate, format) - 1;
        }

        // 返回结果
        return result;
    }

    /**
     * 计算一年的天数
     *
     * @param year
     *            String 年度
     * @return int 天数
     */
    public static int daysOfYear(String year) {
        return dayOfYear(year + "1231", "yyyyMMdd");
    }

    /**
     * 取得当前日期
     *
     * @return String yyyy-mm-dd格式
     */
    public static String getDate() {
        String result = "";
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cal = Calendar.getInstance();
            result = format.format(cal.getTime());
        } catch (Exception e) {
            // Debug.println("日期处理错误");
        }
        return result;
    }

    /**
     * 取得当前日期
     *
     * @return String yyyy-mm-dd hh-mm-ss格式
     */
    public static String getDateTime() {
        String result = "";
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar cal = Calendar.getInstance();
            result = format.format(cal.getTime());
        } catch (Exception e) {
            // Debug.println("日期处理错误");
        }
        return result;
    }

    public static String getDate(String gs) {
        String result = "";
        try {
            SimpleDateFormat format = new SimpleDateFormat(gs);
            Calendar cal = Calendar.getInstance(Locale.CHINA);
            result = format.format(cal.getTime());
        } catch (Exception e) {
            // Debug.println("日期处理错误");
        }
        return result;
    }

    /**
     * 取得日期相应格式的字符串
     *
     * @return String
     */
    public static String getFormatedDate(Calendar calendar, String str_format) {
        String result = "";
        SimpleDateFormat format;
        try {
            format = new SimpleDateFormat(str_format);
            result = format.format(calendar.getTime());
        } catch (Exception e) {
        }
        return result;
    }

    /**
     * 处理“X年X月X日-X年X月X日”的显示格式问题
     *
     * @param rqFrom
     *            String 起始日期
     * @param rqTo
     *            String 结束日期
     * @param inFormat
     *            String 输入日期格式
     * @return String 格式化以后的日期，格式“yyyy年M月d日”或者“M月d日”
     */
    public static String getFromToDateShow(String rqFrom, String rqTo,
                                           String inFormat) {
        String result = "";
        final String defaultFormat = "yyyy-MM-dd";
        String outFormat = "yyyy年M月d日";

        if (rqFrom == null || rqTo == null)
            return result;

        rqFrom = dateFormat(rqFrom, inFormat, defaultFormat);
        rqTo = dateFormat(rqTo, inFormat, defaultFormat);

        // 如果是同一年，不需要显示“年”信息
        if (rqFrom.substring(0, SHORTDATA4).equals(rqTo.substring(0, SHORTDATA4))) {
            outFormat = "M月d日";
        }

        result = dateFormat(rqTo, defaultFormat, outFormat);

        // return
        return result;
    }

    public static String getFromToMonthShow(String rqFrom, String rqTo,
                                            String inFormat) {
        String result = "";
        final String defaultFormat = "yyyyMM";
        String outFormat = "yyyy年M月";

        if (rqFrom == null || rqTo == null)
            return result;

        rqFrom = dateFormat(rqFrom, inFormat, defaultFormat);
        rqTo = dateFormat(rqTo, inFormat, defaultFormat);

        // 如果是同一年，不需要显示“年”信息
        if (rqFrom.substring(0, SHORTDATA4).equals(rqTo.substring(0, SHORTDATA4))) {
            outFormat = "M月";
        }

        result = dateFormat(rqTo, defaultFormat, outFormat);

        // return
        return result;
    }

    /**
     * 字符串转化为整数
     *
     * @param s
     *            输入字符串
     * @return 整数
     *         <p>
     *         出现任何错误，返回0
     */
    public static int string2int(String s) {
        // 校验输入参数
        if (s == null || s.length() == 0)
            return 0;

        // 处理
        int result = 0;
        try {
            result = Integer.valueOf(s).intValue();
        } catch (Exception e) {
            System.out.println(e);
        }
        // 返回值
        return result;
    }

    /**
     * 公式解析 例([@Y123@]+[@Y124@])/[@Y125@]-[@111@]
     *
     * @param s
     * @return
     */
    public static List parseFormula(String s) {
        List data = new ArrayList();
        int a = 0;
        for (int i = 0; i < s.length();) {
            int m = s.indexOf("[@", a);
            int sh = s.indexOf("@]", a + SHORTDATA2);

            String mm = "";
            if (m != -1 && sh != -1)
                mm = s.substring(m + SHORTDATA2, sh);
            else
                sh = s.length();
            if (mm != "" && !mm.equals(""))
                data.add(mm);
            a = sh;
            i = a;
        }
        // System.out.println("sssssssssss---------"+data);
        return data;
    }

    /**
     * 替换字符串
     *
     * @param strSource
     *            --源string
     * @param strFrom
     *            ---要替换的字符串
     * @param strTo
     *            ----替换成的字符串
     * @return
     */
    public static String replacea(String strSource,
                                   String strFrom, String strTo) {
        String strDest = "";
        int intFromLen = strFrom.length();
        int intPos;

        while ((intPos = strSource.indexOf(strFrom)) != -1) {
            strDest = strDest + strSource.substring(0, intPos);
            strDest = strDest + strTo;
            strSource = strSource.substring(intPos + intFromLen);
        }
        strDest = strDest + strSource;

        return strDest;
    }

    /**
     * 截取字符串
     *
     * @param data
     * @return
     */
    public static String getStringByIntercept(String data) {
        for (int i = 0; i < data.length() && data.indexOf("#}") > 0; i++) {
            data = data.substring(0, data.indexOf("{#"))
                    + data.substring(data.indexOf("#}") + SHORTDATA2, data.length());
            // System.out.println(data);
        }
        return data;
    }

    public static String getString(String data) {

        char s[] = { '#', '{', '}' };
        for (int i = 0; i < s.length; i++)
            data = data.replace(s[i], ' ');
        // data.replaceAll("#}", "");
        return data;
    }

    /**
     * 获得组件名称
     *
     * @return 组件名称
     */
    public String toString() {
        return CLASSNAME;
    }
    public static List<String> getPlaceHolder(String regex,String templete) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(templete);
        /*
         * sb用来存储替换过的内容，它会把多次处理过的字符串按源字符串序
         * 存储起来。
         */
        List array=new LinkedList();
        while (matcher.find()) {
            array.add(matcher.group(1));
        }
        return array;
    }
    public static String replaceHolder(String template, Map data)
            throws Exception {
        String regex = "\\$\\{(.+?)\\}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(template);
        /*
         * sb用来存储替换过的内容，它会把多次处理过的字符串按源字符串序
         * 存储起来。
         */
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String name = matcher.group(1);//键名
            String value = (String) data.get(name);//键值
            if (value == null) {
                value = "";
            } else {
                /*
                 * 由于$出现在replacement中时，表示对捕获组的反向引用，所以要对上面替换内容
                 * 中的 $ 进行替换，让它们变成 "\$1000.00" 或 "\$1000000000.00" ，这样
                 * 在下面使用 matcher.appendReplacement(sb, value) 进行替换时就不会把
                 * $1 看成是对组的反向引用了，否则会使用子匹配项值amount 或 balance替换 $1
                 * ，最后会得到错误结果：
                 *
                 * 尊敬的客户刘明你好！本次消费金额amount000.00，您帐户888888888上的余额
                 * 为balance000000.00，欢迎下次光临！
                 *
                 * 要把 $ 替换成 \$ ，则要使用 \\\\\\& 来替换，因为一个 \ 要使用 \\\ 来进
                 * 行替换，而一个 $ 要使用 \\$ 来进行替换，因 \ 与  $ 在作为替换内容时都属于
                 * 特殊字符：$ 字符表示反向引用组，而 \ 字符又是用来转义 $ 字符的。
                 */
                value = value.replaceAll("\\$", "\\\\\\$");
                //System.out.println("value=" + value);
            }
            /*
             * 经过上面的替换操作，现在的 value 中含有 $ 特殊字符的内容被换成了"\$1000.00"
             * 或 "\$1000000000.00" 了，最后得到下正确的结果：
             *
             * 尊敬的客户刘明你好！本次消费金额$1000.00，您帐户888888888上的
             * 余额为$1000000.00，欢迎下次光临！
             *
             * 另外，我们在这里使用Matcher对象的appendReplacement()方法来进行替换操作，而
             * 不是使用String对象的replaceAll()或replaceFirst()方法来进行替换操作，因为
             * 它们都能只能进行一次性简单的替换操作，而且只能替换成一样的内容，而这里则是要求每
             * 一个匹配式的替换值都不同，所以就只能在循环里使用appendReplacement方式来进行逐
             * 个替换了。
             */
            matcher.appendReplacement(sb, value);
        }
        //最后还得要把尾串接到已替换的内容后面去，这里尾串为“，欢迎下次光临！”
        matcher.appendTail(sb);
        return sb.toString();
    }
    // 主函数
    public static void main(String args[]) {
        System.out.println(VsUtil.getPlaceHolder("\\$\\{(.+?)\\}", "${3adid1}sdfsdd9090${3adid2}90sdfsdf90${3adid3}"));
    }

    /**
     * 将"yyyy-MM-dd"格式的字符串格式化为Date类型
     *
     * @param str
     *            待处理的字符串
     * @return 日期，若出现异常ParseException则返回 null
     */
    public static Date parse(String str) {
        return parse(str, "yyyy-MM-dd");
    }

    /**
     * 将指定格式的字符串格式化为Date类型
     *
     * @param str
     *            待处理的字符串
     * @param dateFormat
     *            日期的格式
     * @return 日期，若出现异常ParseException则返回 null
     */
    public static Date parse(String str, String dateFormat) {
        SimpleDateFormat format = new SimpleDateFormat(dateFormat);
        try {
            if (str == null) {
                return null;
            }
            return format.parse(str);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 格式化日期 'yyyy-mm-dd'
     *
     * @param date
     *            待格式化的日期
     * @return 日期字符串格式 'yyyy-mm-dd',date为空时返回""
     */
    public static String format(Date date) {
        return format(date, "yyyy-MM-dd");
    }

    /**
     * 格式化日期 'yyyy-mm-dd'
     *
     * @param date
     *            待格式化的日期
     * @return 日期字符串格式 'yyyy-mm',date为空时返回""
     */
    public static String formatNY(Date date) {
        return format(date, "yyyy-MM");
    }

    public static final String FILEBASEPATH = "D:/vsfile/";

    /**
     * 格式：dateFormat 指定的格式
     *
     * @param date
     *            待格式化的日期
     * @param dateFormat
     *            日期格式
     * @return 日期的字符串格式，date为空时返回""
     */
    public static String format(Date date, String dateFormat) {
        if (date == null)
            return "";
        Format formatter = new SimpleDateFormat(dateFormat);
        return formatter.format(date);
    }





    public static final int READY = 0;

    // Field descriptor #31 Ljava/lang/String;
    public static final String READY_NAME = "准备";

    // Field descriptor #31 Ljava/lang/String;
    public static final int RUNNING = 1;

    // Field descriptor #31 Ljava/lang/String;
    public static final String RUNNING_NAME = "运行";

    // Field descriptor #31 Ljava/lang/String;
    public static final int TERMINATED = 2;

    // Field descriptor #31 Ljava/lang/String;
    public static final String TERMINATED_NAME = "终止";

    // Field descriptor #31 Ljava/lang/String;
    public static final int SUSPENDED = 4;

    // Field descriptor #31 Ljava/lang/String;
    public static final String SUSPENDED_NAME = "暂停";

    public static final int COMMISSION = 5;

    // Field descriptor #31 Ljava/lang/String;
    public static final String COMMISSION_NAME = "委托";

    // Field descriptor #31 Ljava/lang/String;
    public static final int WAITING = 6;

    // Field descriptor #31 Ljava/lang/String;
    public static final String WAITING_NAME = "等待";

    // Field descriptor #31 Ljava/lang/String;
    public static final int ROLLBACK = 7;

    // Field descriptor #31 Ljava/lang/String;
    public static final String ROLLBACK_NAME = "撤销";

    public static final int CHANGE = 8;

    // Field descriptor #31 Ljava/lang/String;
    public static final String CHANGE_NAME = "改派";

    // Field descriptor #31 Ljava/lang/String;
    public static final int COMPLETE = 9;

    // Field descriptor #31 Ljava/lang/String;
    public static final String COMPLETE_NAME = "完成";

    public static String getTwfstatus(String status) {
        int i = 0;
        try {
            i = Integer.parseInt(status);

            String returnstr = "";
            switch (i) {
                case READY:
                    returnstr = READY_NAME;
                    break;
                case RUNNING:
                    returnstr = RUNNING_NAME;
                    break;
                case TERMINATED:
                    returnstr = TERMINATED_NAME;
                    break;
                case SUSPENDED:
                    returnstr = SUSPENDED_NAME;
                    break;
                case WAITING:
                    returnstr = WAITING_NAME;
                    break;
                case ROLLBACK:
                    returnstr = ROLLBACK_NAME;
                    break;
                case COMPLETE:
                    returnstr = COMPLETE_NAME;
                    break;
                case COMMISSION:
                    returnstr = COMMISSION_NAME;
                    break;
                case CHANGE:
                    returnstr = CHANGE_NAME;
                    break;
                default:
                    returnstr = "无效";
                    break;
            }
            return returnstr;
        } catch (Exception e) {
            return "无效";
        }
    }

    /**
     * 为list<Map>的对象建立主键的序列目录
     *
     * @param list
     * @param colum
     * @return
     */
    public static Map creatPkIndexMap(List list, String colum) {
        Map indexMap;
        if (list != null && list.size() > 0) {
            indexMap = new HashMap();
            Map dataMap;
            for (int i = 0; i < list.size(); i++) {
                dataMap = null;
                dataMap = (Map) list.get(i);
                if (dataMap.get(colum) != null) {
                    indexMap.put(dataMap.get(colum), String.valueOf(i));
                }
            }
            return indexMap;
        } else {
            return null;
        }
    }


    public static void childAdd(List parentList, List childList,
                                String parentPk, String childFk, String childName) {
        Map parentListIndexMap = null;// 声明主表对象主键目录哈希对象
        if (parentList != null) {
            parentListIndexMap = creatPkIndexMap(parentList, parentPk);// 声明合同信息对象主键目录哈希对象：主表主键：index
        }
        Map childMap = null;// 声明子表对象
        Map parentMap = null;// 声明对应主表对象
        List childListOfParentMap = null;// 声明对应主表对象中的子表集合
        String indexParent = null;// 主表对象序列
        for (int i = 0; childList != null && i < childList.size(); i++) {
            childMap = (Map) childList.get(i);// 引用子表对象
            if (parentListIndexMap != null) {
                indexParent = (String) parentListIndexMap.get(childMap
                        .get(childFk));// 引用主表对象主键目录哈希对象中的对应主表对象序列
                if (indexParent != null && !indexParent.equals("")) {
                    parentMap = (Map) parentList.get(Integer
                            .parseInt(indexParent));// 引用主表集合中的对应主表对象
                    if (parentMap.get(childName) == null) {
                        childListOfParentMap = new LinkedList();
                        parentMap.put(childName, childListOfParentMap);
                    }
                    childListOfParentMap = (List) parentMap.get(childName);// 引用对应主表对象中的子表对象集合
                    childListOfParentMap.add(childMap);// 子表对象加入到对应子表集合
                }
            }

        }// 遍历子表对象集合
    }

    /**
     * @param parentList
     *            主表集合
     * @param childList
     *            子表集合
     * @param childPksInParent
     *            主表中放置子表主对象
     * @param childPk
     *            子表主键名称
     * @param splitFlag
     *            子表主键字符串分割符
     * @param childName
     *            子表对象在主表中的名称
     */
    public static void childAdd(List parentList, List childList,
                                String childPksInParent, String childPk, String splitFlag,
                                String childName) {
        Map childIndexMap = null;// 声明子表主键序列对象
        if (childList != null) {
            childIndexMap = VsUtil.creatPkIndexMap(childList, childPk);// 引用子表主键序列对象
        }
        if (childIndexMap != null) {
            Map parentMap;// 声明循环中主表对象
            String[] childPks;// 主表中子表主键数组
            List childListOfParent;// 主表所有的子表对象集合
            int indexChild;// 声明对应子表序列
            for (int i = 0; i < parentList.size(); i++) {// 遍历主表集合
                parentMap = (Map) parentList.get(i);// 引用主表对象
                if (parentMap.get(childPksInParent) != null
                        && !((String) parentMap.get(childPksInParent))
                        .equals("")) {
                    childPks = null;// 附件id数组清空
                    if (((String) parentMap.get(childPksInParent))
                            .indexOf(splitFlag) >= 0) {
                        childPks = ((String) parentMap.get(childPksInParent))
                                .split(splitFlag);// 引用子表主键数组
                        childListOfParent = new LinkedList();// 主表对象所有附件对象集合清空
                        for (int j = 0; j < childPks.length; j++) {// 遍历子表主键数组
                            if (childPks[j] != null
                                    && !((String) childPks[j]).equals("")
                                    && childIndexMap.get((String) childPks[j]) != null) {
                                indexChild = Integer.parseInt(childIndexMap
                                        .get((String) childPks[j]).toString());// 得到子表集合中相应的子表序号
                                childListOfParent
                                        .add(childList.get(indexChild));// 引用子表对象集合中相应的子表对象,加到主表对象所有子表对象集合
                            }
                        }
                        parentMap.put(childName, childListOfParent);// 主表对象所有子表对象集合加入到主表对象中
                    }
                }
            }
        }
    }
    /**
     * 为空就返回
     * @param target
     * @param orvalue
     * @return
     */
    public static <T> T emptyOr(T target,T orvalue) {
        if(VsUtil.null2str(target).equals("")) {
            return orvalue;
        }
        return target;
    }

    /**
     * 将map值全部转换为小写
     * @param orgMap
     * @return
     */
    public static Map<String, Object> transformLowerCase(Map<String, Object> orgMap) {
        Map<String, Object> resultMap = new HashMap<>();
        if (orgMap == null || orgMap.isEmpty()) {
            return resultMap;
        }
        Set<String> keySet = orgMap.keySet();
        for (String key : keySet) {
            String newKey = key.toLowerCase();
//            newKey = newKey.replace("_", "");
            resultMap.put(newKey, orgMap.get(key));
        }
        return resultMap;
    }



}
