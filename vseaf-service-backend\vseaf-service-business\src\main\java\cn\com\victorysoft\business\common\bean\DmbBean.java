package cn.com.victorysoft.business.common.bean;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 实体类
 * 代码表
 * <AUTHOR> @date
 * @return
 */
@ApiModel(value = "代码表", description = "代码表")
@Data
public class DmbBean {
    //类别ID
    private String lbid;
    //类别名称
    private String lbmc;
    //编码CODE
    private String code;
    //码值名称
    private String mzmc;
    //父级ID
    private String pid;
    //排序号
    private String pxh;
}
