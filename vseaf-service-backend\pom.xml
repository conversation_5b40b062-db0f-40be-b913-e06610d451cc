<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.com.victorysoft</groupId>
    <artifactId>vseaf-service-backend</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>


    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.1.RELEASE</version>
        <relativePath/>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <druid.version>1.1.18</druid.version>
        <vseaf.version>4.4.2</vseaf.version>
        <poi-ooxml.version>4.1.2</poi-ooxml.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.com.victorysoft</groupId>
                <artifactId>vseaf-service-business</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>cn.com.victorysoft</groupId>
                <artifactId>vseaf-spring-boot-dependencies</artifactId>
                <version>4.4.2</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
           <!-- <dependency>
                <groupId>cn.com.victorysoft</groupId>
                <artifactId>vseaf-spring-boot-starter-security</artifactId>
                <version>${vseaf.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.victorysoft</groupId>
                <artifactId>vseaf-spring-boot-starter-security-cas</artifactId>
                <version>${vseaf.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.victorysoft</groupId>
                <artifactId>vseaf-spring-boot-starter-security-siam</artifactId>
                <version>${vseaf.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.victorysoft</groupId>
                <artifactId>vseaf-spring-boot-starter-security-authm-client</artifactId>
                <version>${vseaf.version}</version>
            </dependency>-->
            
            <dependency>
			    <groupId>org.apache.poi</groupId>
			    <artifactId>poi-ooxml</artifactId>
			    <version>${poi-ooxml.version}</version>
			</dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>3.11</version>
            </dependency>
			
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>vseaf-service-business</module>
        <module>vseaf-service-starter</module>
    </modules>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/webapp</directory>
                <targetPath>META-INF/resources</targetPath>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
            <id>vseaf-repository</id>
            <name>Victorysoft Nexus Repository</name>
            <url>http://10.68.7.181:8081/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

</project>
