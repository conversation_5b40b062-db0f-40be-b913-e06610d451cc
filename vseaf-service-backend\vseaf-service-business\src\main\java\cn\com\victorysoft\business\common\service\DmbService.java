package cn.com.victorysoft.business.common.service;

import cn.com.victorysoft.business.common.bean.*;
import cn.com.victorysoft.business.common.dao.DmbMapper;
import cn.com.victorysoft.vseaf.core.repository.MybatisRepository;
import cn.com.victorysoft.vseaf.core.util.Digests;
import cn.com.victorysoft.vseaf.core.web.http.JsonMessage;
import cn.com.victorysoft.vseaf.util.StringUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 代码 controller
 * @ClassName:DmbService
 * @DATE: 2023/06/09 09:45
 * <AUTHOR>
 */
@Service
public class DmbService {

    @Resource
    private DmbMapper dmbMapper;
    @Resource
    private MybatisRepository mybatisRepository;

    /**
     * 查询代码信息
     * @return JsonMessage
     * <AUTHOR>
     * @Date 2023/06/09 09:46
     */
    public JsonMessage queryDmList(DmbBean bean) {
        List<DmbBean> list =  dmbMapper.queryDmList(bean);
        return new JsonMessage().success(list);
    }


    /**
     * 查询组织机构
     * @param treeFlag yes 返回树结构 no 返回列表
     * @return JsonMessage
     * <AUTHOR>
     * @Date 2023/06/09 09:48
     */
    public JsonMessage queryOrgList(DepartmentBean bean, String treeFlag) {
        List<DepartmentBean> list =  dmbMapper.queryOrgList(bean);
        if("yes".equals(treeFlag)){
            String pid = "0";
            if(!"".equals(StringUtil.null2blank(bean.getPorgnaId()))){
                pid = bean.getPorgnaId();
            }
            return new JsonMessage().success(getOrgTree(list,pid));
        }
        return new JsonMessage().success(list);
    }

    /**
     * 递归组织机构树结构
     * <AUTHOR>
     * @param list 集合
     * @param pValue 顶级节点的值
     * @return 树结构
     */
    public static List<DepartmentBean> getOrgTree(List<DepartmentBean> list, String pValue) {
        if(pValue==null || "".equals(pValue)){
            pValue = "0";
        }
        List<DepartmentBean> res = new ArrayList<>();
        if (list!= null && !list.isEmpty()){
            for (DepartmentBean department : list) {
                if(
                        department.getPorgnaId() != null &&
                        (pValue.equals(department.getPorgnaId()) || "".equals(department.getPorgnaId()))
                        ||  (department.getPorgnaId() != null && department.getPorgnaId().equals(pValue))
                ){
                    String id = department.getOrgnaId();
                    boolean childrenFlag=false;
                    for(int i=0;i<list.size();i++){
                        if(list.get(i).getPorgnaId().equals(id)){
                            childrenFlag=true;
                            break;
                        }
                    }
                    if(childrenFlag){
                        department.setChildren(getOrgTree(list, id));
                    }
                    res.add(department);
                }
            }
        }
        return res;
    }

    /**
     * 修改用户密码
     * <AUTHOR>
     * @param entUser 用户实体
     */
    @Transactional
    public JsonMessage updateUserById(EntUser entUser) {
        String psw = StringUtil.null2blank(entUser.getUserPassword());
        if (!"".equals(psw)) {
            entUser.setUserPassword(Digests.md5(psw));
        }
        mybatisRepository.update("cn.com.victorysoft.business.common.dao.DmbMapper.updateEntUserPassword", entUser);
        return new JsonMessage().success("修改成功");
    }

}
