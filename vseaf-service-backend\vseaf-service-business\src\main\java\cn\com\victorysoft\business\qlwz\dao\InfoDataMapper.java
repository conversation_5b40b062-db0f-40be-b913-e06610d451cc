package cn.com.victorysoft.business.qlwz.dao;

import cn.com.victorysoft.business.qlwz.bean.InfoDataBean;
import cn.com.victorysoft.business.qlwz.bean.InfoDataMonitorBean;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
public interface InfoDataMapper {
    /**
     * 查询门户信息
     */
    List queryInfoDataList(InfoDataBean infoDataBean);
    /**
     * 保存门户信息
     */
    void saveInfoData(InfoDataBean infoDataBean);
    /**
     * 删除门户信息
     */
    void deleteInfoData(String infoId);
    /**
     * 根据主键infoId查询门户信息
     */
    InfoDataBean queryInfoDataById(String infoId);
    /**
     * 查询纪发课堂的前三条最新数据
     */
    List queryInfoData(InfoDataMonitorBean infoDataMonitorBean);
    /**
     * 查询纪发课堂所有提交的视频
     */
    List queryInfoList(InfoDataMonitorBean infoDataMonitorBean);

    /**
     * 通过上传文件的业务id查询出类型是视频类型的信息
     */
    InfoDataBean queryVideo(InfoDataBean infoDataBean);
}
