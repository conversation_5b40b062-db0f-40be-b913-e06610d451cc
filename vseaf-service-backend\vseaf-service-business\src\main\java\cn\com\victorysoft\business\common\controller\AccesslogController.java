package cn.com.victorysoft.business.common.controller;

import cn.com.victorysoft.business.common.service.AccesslogService;
import cn.com.victorysoft.vseaf.core.web.http.JsonMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 系统日志 controller
 * @ClassName: AccesslogController
 * @DATE: 2023/05/23
 * <AUTHOR>
 */
@RestController
@Api(tags = "accesslogController")
@RequestMapping("/accesslogController")
public class AccesslogController {

    @Autowired
    AccesslogService accesslogService;

    /**
     * 存储登出日志
     */
    @PostMapping("/insertLog")
    @ApiOperation(value = "insertLog", notes = "退出系统")
    public JsonMessage insertLog(@RequestBody Map<String, Object> params){
        return accesslogService.insertLog(params);
    }
}
