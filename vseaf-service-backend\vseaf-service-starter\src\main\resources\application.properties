#\u5F53\u9047\u5230\u540C\u6837\u540D\u5B57\u7684\u65F6\u5019\uFF0C\u662F\u5426\u5141\u8BB8\u8986\u76D6\u6CE8\u518C
spring.main.allow-bean-definition-overriding=true
#\u65E5\u5FD7
logging.level.cn.com.victorysoft=debug
#\u5E94\u7528\u540D\u79F0
spring.application.name=vseaf-service
#\u7AEF\u53E3
server.port=7777
#\u9879\u76EE\u865A\u62DF\u8DEF\u5F84
server.servlet.context-path=/

#oracle\u6570\u636E\u5E93
#spring.datasource.driver-class-name=oracle.jdbc.driver.OracleDriver
#spring.datasource.url=**************************************
#spring.datasource.username=QLGCY
#spring.datasource.password=Qlgcy_2024_Qlgcy
#hL/kVEHGjew9iqJ5zteuJV35LxngXASQxRni9d256I4=
#mysql\u6570\u636E\u5E93
#spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.url=*******************************************************************************************************************************************
#spring.datasource.username=zhjy
#spring.datasource.password=zhjy123

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=**********************************************************************************************************************************************
spring.datasource.username=tzkygysgl
spring.datasource.password=j4rXX0CZ

#
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.initial-size=8   
spring.datasource.druid.min-idle=1 
spring.datasource.druid.max-active=20
spring.datasource.druid.max-wait=60000
spring.datasource.druid.time-between-eviction-runsMillis=60000
spring.datasource.druid.min-evictable-idle-timeMillis=300000
spring.datasource.druid.validation-query=select 'x' FROM DUAL
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.pool-prepared-statements=false
spring.thymeleaf.cache=false
mybatis.configuration.map-underscore-to-camel-case=true
#swagger\u914D\u7F6E
vseaf.swagger.enabled=true
vseaf.swagger.info.base-package=cn.com.victorysoft

#\u6743\u9650\u670D\u52A1
#vseaf.security.authm.url=http://localhost:8077/authm-center
#####=========\u4E0A\u4F20\u670D\u52A1-\u5C5E\u6027\u914D\u7F6E=====#########################
#\u652F\u6301\u7684\u6587\u4EF6\u7C7B\u578B
vsupload.file-type=doc,docx,jpg,png,pdf,xls,xlsx,ppt,pptx,gif,txt,DOC,DOCX,JPG,PNG,PDF,XLS,XLSX,PPT,PPTX,GIF,TXT,jpeg,JPEG,bmp,BMP,mp4,MP4,rar,zip,RAR,ZIP
#\u5B58\u50A8\u7C7B\u578B,\u9ED8\u8BA4\u56DB\u79CD\uFF1A1-\u672C\u5730\u5B58\u50A8;2-\u5173\u7CFB\u6570\u636E\u5E93;3-ftp\u4E0A\u4F20;4-mongodb
# \u5176\u4E2Dbbb\u4E3A\u4E1A\u52A1\u7C7B\u578B\uFF0C\u8868\u793A\uFF1A\u4E1A\u52A1\u7C7B\u578B\u4E3Abbb\u7684\u5B58\u50A8\u7C7B\u578B\u4E3A\u6570\u636E\u5E93\uFF0C\u53EF\u4EE5\u914D\u7F6E\u591A\u4E2A\uFF0C\u4E1A\u52A1\u7C7B\u578B\u5FC5\u987B\u4E0D\u540C
#\u9644\u4EF6\u7C7B\u578B
vsupload.storage-type.project_files=1
#\u5B58\u50A8\u8DEF\u5F84\uFF0C\u53EF\u4EE5\u914D\u7F6E\u591A\u4E2A\uFF0C\u4E1A\u52A1\u7C7B\u578B\u5FC5\u987B\u4E0D\u540C
#vsupload.storage-path.bbb=e:/a/b/c\uFF0C\u8868\u793A\u4E1A\u52A1\u7C7B\u578B\u4E3Abbb\u7684\u5B58\u50A8\u8DEF\u5F84\u4E3Ae:/b/{yyyy}/{MM}/{dd}
#\u5176\u4E2D{yyyy}:\u5F53\u524D\u5E74\u4EFD;{MM}:\u5F53\u524D\u6708\u4EFD;{dd}:\u65E5\u671F\uFF0C\u4E5F\u53EF\u4EE5\u914D\u7F6E\u5176\u4ED6\u7684\u65E5\u671F\u683C\u5F0F
vsupload.storage-path.project_files=D:/vseaf/project_files

#\u6587\u4EF6\u4E0A\u4F20\u914D\u7F6E
spring.servlet.multipart.max-file-size=204800MB
spring.servlet.multipart.max-request-size=204800MB
server.compression.enabled=true
server.compression.min-response-size=10KB

# http://*************:13000
#\u5E94\u7528\u7F16\u7801\uFF0C\u552F\u4E00\uFF0C\u4E0E\u6743\u9650\u4E2D\u5FC3\u4E2D\u6CE8\u518C\u5E94\u7528\u7684\u5E94\u7528code\u4E00\u81F4
app.code=AUTHM
#\u6743\u9650\u63A5\u53E3\u670D\u52A1\u5730\u5740
vscloud.client.authm-api.url=http://localhost:8848/authm-api

#\u4F01\u4E1AID\uFF08\u63A5\u53E3\u8BA4\u8BC1\u65F6\u7528\u5230\uFF09
vscloud.validate.tenant-id=D7ECD5897BAE4D09BDFAFCE1C8F4158B
#\u5E94\u7528\u79D8\u94A5\u7BA1\u7406\u529F\u80FD\u9881\u53D1\u7684\u79D8\u94A5\uFF08\u63A5\u53E3\u8BA4\u8BC1\u65F6\u7528\u5230\uFF09
vscloud.validate.authm.secret-key=9d3931c8d42058e34c5426836782804b
#\u5E94\u7528\u7F16\u53F7\uFF08\u63A5\u53E3\u8BA4\u8BC1\u65F6\u7528\u5230\uFF09
vscloud.validate.authm.area-code=AUTHM
#\u5FFD\u7565\u62E6\u622A\u8DEF\u5F84\uFF0C\u591A\u4E2A\u7528\u9017\u53F7\u9694\u5F00
vseaf.security.ignored=/api/v5/**,/authm/ws/rest/**,/vscomponent/fileupload/**,/qlwz/**,/shwhController/**
#\u62E6\u622A\u8DEF\u5F84\uFF0C\u5F15\u7528vs-authm-backend-secret\u540E\u9700\u8981\u914D\u7F6E
vsauthm.rest.interceptor.url=/api/v5/**,/authm/ws/rest/**


#\u5B89\u5168\u914D\u7F6E
vseaf.http.protect-enabled=false
security.host.whitelist=localhost,localhost:8089,localhost:8088,localhost:7777,**************,**************:7777,**************:8089,**************:8088,*************:8089,*************:7777,*************:8081,*************,*************,*************:8088,*************:7777
server.servlet.session.timeout=1720000

#\u914D\u7F6Eweb\u9875\u9762\u5165\u53E3
magic-api.web=/magic/web
#\u914D\u7F6E\u6587\u4EF6\u5B58\u50A8\u4F4D\u7F6E\u3002\u5F53\u4EE5classpath\u5F00\u5934\u65F6\uFF0C\u4E3A\u53EA\u8BFB\u6A21\u5F0F
magic-api.resource.location=/data/magic-api


spring.data.redis.repositories.enabled=false
management.health.redis.enabled=false

