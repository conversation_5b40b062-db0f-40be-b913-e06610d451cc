package cn.com.victorysoft.business.common.bean;

import lombok.Data;

import java.util.Date;

/**
 * 实体类  EntUser
 * <AUTHOR> @date
 * @return
 */
@Data
public class EntUser {
    private String userId;
    /**
     * 用户code
     * <AUTHOR> @date
     * @return
     */
    private String userCode;
    /**
     * userLoginname
     * <AUTHOR> @date
     * @return
     */
    private String userLoginname;
    /**
     * userName
     * <AUTHOR> @date
     * @return
     */
    private String userName;
    /**
     * userPassword
     * <AUTHOR> @date
     * @return
     */
    private String userPassword;
    /**
     * userDesc
     * <AUTHOR> @date
     * @return
     */
    private String userDesc;
    /**
     * userValidityStart
     * <AUTHOR> @date
     * @return
     */
    private Date userValidityStart;
    /**
     * userValidityEnd
     * <AUTHOR> @date
     * @return
     */
    private Date userValidityEnd;
    /**
     * orgnaId
     * <AUTHOR> @date
     * @return
     */
    private String orgnaId;
    /**
     * orgnaName
     * <AUTHOR> @date
     * @return
     */
    private String orgnaName;
    /**
     * userIp
     * <AUTHOR> @date
     * @return
     */
    private String userIp;
    /**
     * enabled
     * <AUTHOR> @date
     * @return
     */
    private String enabled;
    /**
     * userEmail
     * <AUTHOR> @date
     * @return
     */
    private String userEmail;
    /**
     * userMobile
     * <AUTHOR> @date
     * @return
     */
    private String userMobile;
    /**
     * userSex
     * <AUTHOR> @date
     * @return
     */
    private String userSex;
    /**
     * userCard
     * <AUTHOR> @date
     * @return
     */
    private String userCard;
    /**
     * userAd
     * <AUTHOR> @date
     * @return
     */
    private String userAd;
    /**
     * userLdap
     * <AUTHOR> @date
     * @return
     */
    private String userLdap;
    /**
     * isAdmin
     * <AUTHOR> @date
     * @return
     */
    private String isAdmin;

}