package cn.com.victorysoft.business.shwh.service;

import cn.com.victorysoft.business.shwh.bean.ShwhBean;
import cn.com.victorysoft.vseaf.core.mybatis.paging.bean.DataPaging;
import cn.com.victorysoft.vseaf.core.mybatis.paging.bean.PageRequest;
import cn.com.victorysoft.vseaf.core.repository.MybatisRepository;
import cn.com.victorysoft.vseaf.core.web.http.JsonMessage;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 审核维护 service
 * @package: cn.com.victorysoft.business.shwh.service
 * @className: ShwhService
 * @author: kongshifeng
 * @date: 2025/7/22 16:29
 */
@Service
@Component
public class ShwhService {

    @Resource
    MybatisRepository mybatisRepository;

    private final String namespace = "cn.com.victorysoft.business.shwh.dao.ShwhMapper.";

    /**
     * 查询审核人信息列表
     * @return JsonMessage
     * <AUTHOR>
     * @param shwhBean
     */
    public JsonMessage selectShwhList(ShwhBean shwhBean){
        List<ShwhBean> shwhBeanList = mybatisRepository.selectByParams(namespace+"selectShwhList",shwhBean);
        return new JsonMessage().success(shwhBeanList);
    }

    /**
     * 删除单个审核人信息
     * @return JsonMessage
     * <AUTHOR>
     * @param shwhBean
     */
    public JsonMessage deleteOneShwh(ShwhBean shwhBean) {
        Map resultMap = new HashMap();
        String PZID = shwhBean.getPZID();
        try {
            mybatisRepository.delete(namespace + "deleteOneShwh", PZID);
            resultMap.put("status","success");
            resultMap.put("message","删除成功");
        }catch (Exception e){
            resultMap.put("status","fail");
            resultMap.put("message","删除失败");
        }
        return new JsonMessage().success(resultMap);
    }

    /**
     * 保存审核人信息
     * @return JsonMessage
     * <AUTHOR>
     * @param shwhBean
     */
    @Transactional
    public JsonMessage insertShwhList(ShwhBean shwhBean) {
        Map result=new HashMap();
        try{
            mybatisRepository.update(namespace+"insertShwhList", shwhBean);
            result.put("status","success");
            result.put("message","保存成功");
        }catch (Exception e){
            result.put("status","fail");
            result.put("message","保存失败");
        }

        return new JsonMessage().success(result);
    }

    /**
     * 查询审人员选择列表
     * @return JsonMessage
     * <AUTHOR>
     * @param shwhBean
     */
    public JsonMessage selectUserSelectInfo(ShwhBean shwhBean) {
        int pageIndex = Integer.parseInt(shwhBean.getPageIndex());
        int pageSize = Integer.parseInt(shwhBean.getPageSize());
        PageRequest pageRequest = new PageRequest<>(pageIndex,pageSize,shwhBean);
        DataPaging dataPaging = mybatisRepository.selectPaging(namespace+"selectUserSelectInfo", pageRequest);
        return new JsonMessage().success(dataPaging);
    }

}
